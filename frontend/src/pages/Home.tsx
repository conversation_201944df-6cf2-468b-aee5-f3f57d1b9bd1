import React from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/useAuth";
import {
  Store,
  Package,
  ShoppingCart,
  BarChart3,
  ArrowRight,
  Sparkles,
  Zap,
  Shield,
} from "lucide-react";

const Home: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="max-w-7xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-20 px-4 animate-fade-in">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl mb-8 shadow-lg animate-bounce">
          <Sparkles className="w-10 h-10 text-white" />
        </div>

        <h1 className="text-6xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-blue-600 to-purple-600 bg-clip-text text-transparent leading-tight">
          E-commerce Integration Hub
        </h1>

        <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
          Connect and manage your{" "}
          <span className="font-semibold text-blue-600">Shopify</span> and{" "}
          <span className="font-semibold text-purple-600">WooCommerce</span>{" "}
          stores from one central dashboard. Sync products, track inventory, and
          monitor orders across all your e-commerce platforms.
        </p>

        {!user ? (
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              to="/register"
              className="btn-primary text-lg px-10 py-4 group"
            >
              Get Started Free
              <ArrowRight
                className="ml-3 group-hover:translate-x-1 transition-transform"
                size={24}
              />
            </Link>
            <Link to="/login" className="btn-secondary text-lg px-10 py-4">
              Sign In
            </Link>
          </div>
        ) : (
          <Link
            to="/dashboard"
            className="btn-primary text-lg px-10 py-4 group"
          >
            Go to Dashboard
            <ArrowRight
              className="ml-3 group-hover:translate-x-1 transition-transform"
              size={24}
            />
          </Link>
        )}
      </div>

      {/* Features Section */}
      <div className="py-20 px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Powerful Features
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to manage your e-commerce empire from one place
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="card-modern text-center group">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
              <Store className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-3 text-gray-900">
              Multi-Store Management
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Connect multiple Shopify and WooCommerce stores in one unified
              dashboard
            </p>
          </div>

          <div className="card-modern text-center group">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
              <Package className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-3 text-gray-900">
              Smart Product Sync
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Automatically sync products and inventory across all platforms in
              real-time
            </p>
          </div>

          <div className="card-modern text-center group">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
              <ShoppingCart className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-3 text-gray-900">
              Order Management
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Track and manage orders from all your stores with advanced
              filtering and search
            </p>
          </div>

          <div className="card-modern text-center group">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
              <BarChart3 className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-3 text-gray-900">
              Advanced Analytics
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Get deep insights and analytics across all your e-commerce
              platforms
            </p>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="py-20 px-4">
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl mb-6 shadow-lg">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-4 text-gray-900">
              Lightning Fast
            </h3>
            <p className="text-gray-600 text-lg leading-relaxed">
              Built for speed with modern technology stack ensuring quick
              response times
            </p>
          </div>

          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl mb-6 shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-4 text-gray-900">
              Secure & Reliable
            </h3>
            <p className="text-gray-600 text-lg leading-relaxed">
              Enterprise-grade security with 99.9% uptime guarantee for your
              business
            </p>
          </div>

          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl mb-6 shadow-lg">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-4 text-gray-900">
              Easy to Use
            </h3>
            <p className="text-gray-600 text-lg leading-relaxed">
              Intuitive interface designed for both beginners and e-commerce
              experts
            </p>
          </div>
        </div>
      </div>

      {/* Supported Platforms */}
      <div className="py-20 px-4">
        <div className="card-modern text-center max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-gray-900 mb-8">
            Supported Platforms
          </h2>
          <p className="text-xl text-gray-600 mb-12">
            Seamlessly integrate with the most popular e-commerce platforms
          </p>

          <div className="flex justify-center items-center gap-16">
            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-elegant group-hover:scale-110 transition-transform duration-200">
                <span className="text-white font-bold text-2xl">S</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Shopify</span>
              <p className="text-gray-600 mt-1">Full API Integration</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <span className="text-white font-bold text-2xl">W</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                WooCommerce
              </span>
              <p className="text-gray-600 mt-1">REST API Support</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
