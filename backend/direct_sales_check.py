#!/usr/bin/env python3
"""
Direct check of sales data vs predictions using the forecasting service
"""

import requests
import json

def direct_sales_check():
    """Check sales data and predictions using the forecast with history endpoint"""
    
    print("🔍 Direct Sales Reality Check...")
    print("=" * 60)
    
    try:
        # Use the forecast endpoint that includes historical data
        response = requests.get("http://localhost:8000/api/products/1/forecast?forecast_period=1M")
        
        if response.status_code == 401:
            print("❌ Authentication required - trying debug endpoint instead...")
            # Fall back to debug endpoint
            response = requests.get("http://localhost:8000/api/products/debug/1/forecast?forecast_period=1M")
        
        if response.status_code != 200:
            print(f"❌ Failed to get forecast data: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return
        
        data = response.json()
        
        # Check if we have historical data
        historical_data = data.get("historical_data", [])
        
        if not historical_data:
            print("❌ No historical data in response")
            print(f"Available keys: {list(data.keys())}")
            
            # Try to get sales data from the multi-model forecast response
            if "forecasts" in data:
                print(f"\n📊 Available forecasts: {list(data['forecasts'].keys())}")
                
                # Get predictions
                forecasts = data["forecasts"]
                
                if "Prophet" in forecasts and "FB_Prophet" in forecasts:
                    prophet_pred = forecasts["Prophet"]["forecast_data"][0]["predicted_sales"]
                    fb_prophet_pred = forecasts["FB_Prophet"]["forecast_data"][0]["predicted_sales"]
                    
                    print(f"\n📈 Predictions:")
                    print(f"  Prophet: {prophet_pred:.2f} units/day")
                    print(f"  FB_Prophet: {fb_prophet_pred:.2f} units/day")
                    print(f"  Difference: {abs(fb_prophet_pred - prophet_pred):.2f} units/day")
                    print(f"  Ratio: {fb_prophet_pred/prophet_pred:.2f}x")
                    
                    # Check if they're too different
                    diff_pct = abs(fb_prophet_pred - prophet_pred) / prophet_pred * 100
                    if diff_pct > 30:
                        print(f"  ❌ ISSUE: {diff_pct:.1f}% difference between Prophet models!")
                        print(f"     They should be nearly identical since they use the same library")
                    else:
                        print(f"  ✅ Prophet models are reasonably close ({diff_pct:.1f}% difference)")
                
                # Check historical data points
                historical_points = data.get("historical_data_points", 0)
                print(f"\n📊 Historical data points: {historical_points}")
                
                if historical_points > 0:
                    print(f"  ✅ Sufficient data for forecasting")
                else:
                    print(f"  ❌ No historical data available")
                
                # Without actual sales values, we can't determine if predictions are reasonable
                print(f"\n⚠️  Cannot determine if predictions are reasonable without actual sales values")
                print(f"   Need to access the raw sales data to compare with predictions")
                
            return
        
        # If we have historical data, analyze it
        print(f"📊 Historical Data Found: {len(historical_data)} records")
        
        # Extract actual sales values
        actual_sales = []
        dates = []
        
        for record in historical_data:
            if isinstance(record, dict):
                sales = record.get("actual_sales", record.get("quantity_sold", 0))
                date = record.get("date", "")
                if sales is not None:
                    actual_sales.append(sales)
                    dates.append(date)
        
        if not actual_sales:
            print("❌ No sales values found in historical data")
            return
        
        # Calculate statistics
        total_sales = sum(actual_sales)
        avg_sales = total_sales / len(actual_sales)
        max_sales = max(actual_sales)
        min_sales = min(actual_sales)
        zero_days = sum(1 for s in actual_sales if s == 0)
        non_zero_sales = [s for s in actual_sales if s > 0]
        
        print(f"\n📈 Sales Statistics:")
        print(f"  Total records: {len(actual_sales)}")
        print(f"  Average daily sales: {avg_sales:.2f} units/day")
        print(f"  Min: {min_sales}, Max: {max_sales}")
        print(f"  Zero sales days: {zero_days}/{len(actual_sales)} ({zero_days/len(actual_sales)*100:.1f}%)")
        
        if non_zero_sales:
            avg_non_zero = sum(non_zero_sales) / len(non_zero_sales)
            print(f"  Average on non-zero days: {avg_non_zero:.2f} units/day")
        
        # Recent sales analysis
        recent_sales = actual_sales[-30:] if len(actual_sales) >= 30 else actual_sales
        recent_avg = sum(recent_sales) / len(recent_sales)
        print(f"  Recent average ({len(recent_sales)} days): {recent_avg:.2f} units/day")
        
        # Show recent sales
        print(f"\n📋 Recent Sales (last 10 days):")
        for i in range(max(0, len(actual_sales) - 10), len(actual_sales)):
            date = dates[i] if i < len(dates) else f"Day {i+1}"
            sales = actual_sales[i]
            print(f"  {date}: {sales} units")
        
        # Get predictions from forecast data
        forecast_data = data.get("forecast_data", [])
        if forecast_data:
            first_prediction = forecast_data[0]["predicted_sales"]
            print(f"\n📈 Ensemble Prediction: {first_prediction:.2f} units/day")
            
            # Reality check
            print(f"\n🎯 Reality Check:")
            print(f"  Historical average: {avg_sales:.2f} units/day")
            print(f"  Recent average: {recent_avg:.2f} units/day")
            print(f"  Ensemble prediction: {first_prediction:.2f} units/day")
            
            # Analysis
            if recent_avg < 1 and first_prediction > 4:
                print(f"  ❌ MAJOR ISSUE: Recent sales ({recent_avg:.2f}) much lower than prediction ({first_prediction:.2f})")
            elif abs(recent_avg - first_prediction) <= 2:
                print(f"  ✅ Prediction seems reasonable")
            else:
                print(f"  ⚠️  Prediction may be off by {abs(recent_avg - first_prediction):.1f} units/day")
        
        # Sparse data analysis
        if zero_days / len(actual_sales) > 0.5:
            print(f"\n💡 INSIGHT: {zero_days/len(actual_sales)*100:.1f}% zero-sales days indicates sparse sales pattern")
            print(f"   This makes forecasting very challenging")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    direct_sales_check()
