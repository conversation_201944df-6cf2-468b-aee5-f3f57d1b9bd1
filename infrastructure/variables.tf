variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
  default     = "ecommerce-hub"
}

variable "region" {
  description = "GCP region"
  type        = string
  default     = "us-central1"
}

variable "database_name" {
  description = "Database name"
  type        = string
  default     = "ecommerce_db"
}

variable "database_user" {
  description = "Database user"
  type        = string
  default     = "app_user"
}

variable "database_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

variable "jwt_secret_key" {
  description = "JWT secret key"
  type        = string
  sensitive   = true
}
