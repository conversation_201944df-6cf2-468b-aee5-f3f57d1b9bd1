# backend/create_fake_shopify_orders.py

import os
import random
import asyncio
from datetime import datetime, timedelta
import httpx
import time

SHOP_DOMAIN = os.getenv("SHOP_DOMAIN", "leanchain.myshopify.com")  # Set your shop domain
ADMIN_ACCESS_TOKEN = os.getenv("SHOPIFY_ADMIN_ACCESS_TOKEN", "your-access-token")  # Set your admin access token

API_VERSION = "2024-01"
ORDERS_ENDPOINT = f"https://{SHOP_DOMAIN}/admin/api/{API_VERSION}/orders.json"
PRODUCTS_ENDPOINT = f"https://{SHOP_DOMAIN}/admin/api/{API_VERSION}/products.json"

headers = {
    "Content-Type": "application/json",
    "X-Shopify-Access-Token": ADMIN_ACCESS_TOKEN,
}


async def fetch_products():
    async with httpx.AsyncClient() as client:
        response = await client.get(PRODUCTS_ENDPOINT, headers=headers)
        response.raise_for_status()
        products = response.json().get("products", [])
        variants = []
        for product in products:
            for variant in product.get("variants", []):
                variants.append(
                    {
                        "product_id": product["id"],
                        "variant_id": variant["id"],
                        "title": product["title"],
                        "variant_title": variant["title"],
                        "price": float(variant["price"]),
                    }
                )
        return variants


def random_order_data(variant):
    now = datetime.now()
    order_date = now - timedelta(days=random.randint(0, 365))  # Extend to 1 year for more variety

    # Format the date properly for Shopify API (ISO 8601 with timezone)
    formatted_date = order_date.strftime("%Y-%m-%dT%H:%M:%S%z")
    if not formatted_date.endswith(("Z", "+00:00", "-00:00")):
        # Add timezone if not present
        formatted_date = order_date.strftime("%Y-%m-%dT%H:%M:%S") + "+00:00"

    return {
        "order": {
            "line_items": [
                {
                    "variant_id": variant["variant_id"],
                    "quantity": random.randint(1, 3),
                }
            ],
            "customer": {
                "first_name": random.choice(["John", "Jane", "Alex", "Sam"]),
                "last_name": random.choice(["Doe", "Smith", "Lee", "Patel"]),
                "email": f"test{random.randint(1000,9999)}@example.com",
            },
            "financial_status": "paid",
            "processed_at": formatted_date,
        }
    }


async def create_fulfillment(client, order_id, line_items):
    """Create a fulfillment for an order to mark it as fulfilled"""
    fulfillment_endpoint = f"https://{SHOP_DOMAIN}/admin/api/{API_VERSION}/orders/{order_id}/fulfillments.json"

    fulfillment_data = {
        "fulfillment": {
            "line_items": [{"id": item["id"], "quantity": item["quantity"]} for item in line_items],
            "notify_customer": False,
            "tracking_company": random.choice(["UPS", "FedEx", "DHL", "USPS"]),
            "tracking_number": f"1Z{random.randint(100000000000000, 999999999999999)}",
        }
    }

    try:
        response = await client.post(fulfillment_endpoint, headers=headers, json=fulfillment_data)
        response.raise_for_status()
        return True
    except Exception as e:
        print(f"Failed to create fulfillment for order {order_id}: {e}")
        return False


async def create_fake_orders(n=1000):
    variants = await fetch_products()
    if not variants:
        print("No products/variants found in your Shopify store. Please add products first.")
        return
    # Use only the first 5 products (all their variants)
    product_ids = []
    first_5_variants = []
    for variant in variants:
        if variant["product_id"] not in product_ids:
            product_ids.append(variant["product_id"])
        if len(product_ids) > 5:
            break
        if variant["product_id"] in product_ids[:5]:
            first_5_variants.append(variant)
    if not first_5_variants:
        print("No variants found for the first 5 products.")
        return
    async with httpx.AsyncClient() as client:
        i = 0
        attempts = 0
        while i < n:
            variant = random.choice(first_5_variants)
            order_data = random_order_data(variant)

            # Debug: Print the date we're trying to set
            requested_date = order_data["order"]["processed_at"]
            print(f"Attempting to create order with processed_at: {requested_date}")

            try:
                response = await client.post(ORDERS_ENDPOINT, headers=headers, json=order_data)
                await asyncio.sleep(5)
                if response.status_code == 429:
                    # Exponential backoff
                    wait_time = 20
                    print(f"429 Too Many Requests. Backing off for {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                    attempts += 1
                    continue
                response.raise_for_status()
                order_response = response.json().get("order", {})
                order_id = order_response.get("id")
                actual_created_at = order_response.get("created_at")
                actual_processed_at = order_response.get("processed_at")

                # Debug: Print what Shopify actually created
                print(f"Shopify created order with:")
                print(f"  created_at: {actual_created_at}")
                print(f"  processed_at: {actual_processed_at}")

                # Randomly fulfill some orders (60% chance)
                should_fulfill = random.random() < 0.6
                fulfillment_status = "unfulfilled"

                if should_fulfill:
                    line_items = order_response.get("line_items", [])
                    if line_items:
                        await asyncio.sleep(2)  # Small delay before creating fulfillment
                        if await create_fulfillment(client, order_id, line_items):
                            fulfillment_status = "fulfilled"

                print(
                    f"Created fake order {i+1} for product '{variant['title']}' (variant '{variant['variant_title']}'): {order_id} - {fulfillment_status}"
                )
                i += 1
                attempts = 0  # Reset attempts after a successful request
            except Exception as e:
                print(f"Failed to create order {i+1}: {e}")
                attempts += 1
                await asyncio.sleep(min(2**attempts, 60))


if __name__ == "__main__":
    asyncio.run(create_fake_orders(5))  # Create 5 orders for testing
