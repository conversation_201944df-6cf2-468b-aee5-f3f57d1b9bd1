import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from main import app
from database import get_db, Base
from models import User

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

@pytest.fixture
def test_user():
    return {
        "email": "<EMAIL>",
        "password": "testpassword",
        "full_name": "Test User"
    }

def test_register_user(test_user):
    """Test user registration"""
    response = client.post("/api/auth/register", json=test_user)
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == test_user["email"]
    assert data["full_name"] == test_user["full_name"]
    assert "id" in data

def test_register_duplicate_email(test_user):
    """Test registration with duplicate email"""
    # Register first user
    client.post("/api/auth/register", json=test_user)
    
    # Try to register again with same email
    response = client.post("/api/auth/register", json=test_user)
    assert response.status_code == 400
    assert "already registered" in response.json()["detail"]

def test_login_user(test_user):
    """Test user login"""
    # Register user first
    client.post("/api/auth/register", json=test_user)
    
    # Login
    login_data = {
        "email": test_user["email"],
        "password": test_user["password"]
    }
    response = client.post("/api/auth/login", json=login_data)
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_login_invalid_credentials():
    """Test login with invalid credentials"""
    login_data = {
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }
    response = client.post("/api/auth/login", json=login_data)
    assert response.status_code == 401
    assert "Incorrect email or password" in response.json()["detail"]

def test_get_current_user(test_user):
    """Test getting current user info"""
    # Register and login
    client.post("/api/auth/register", json=test_user)
    login_response = client.post("/api/auth/login", json={
        "email": test_user["email"],
        "password": test_user["password"]
    })
    token = login_response.json()["access_token"]
    
    # Get current user
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/auth/me", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == test_user["email"]
    assert data["full_name"] == test_user["full_name"]

def test_get_current_user_invalid_token():
    """Test getting current user with invalid token"""
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/api/auth/me", headers=headers)
    assert response.status_code == 401

def test_register_invalid_email():
    """Test registration with invalid email"""
    invalid_user = {
        "email": "invalid-email",
        "password": "testpassword",
        "full_name": "Test User"
    }
    response = client.post("/api/auth/register", json=invalid_user)
    assert response.status_code == 422  # Validation error

def test_register_missing_fields():
    """Test registration with missing required fields"""
    incomplete_user = {
        "email": "<EMAIL>"
        # Missing password and full_name
    }
    response = client.post("/api/auth/register", json=incomplete_user)
    assert response.status_code == 422  # Validation error
