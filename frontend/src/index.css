@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");
@import "tailwindcss";

@theme {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  --color-secondary-950: #020617;

  --color-accent-50: #fdf4ff;
  --color-accent-100: #fae8ff;
  --color-accent-200: #f5d0fe;
  --color-accent-300: #f0abfc;
  --color-accent-400: #e879f9;
  --color-accent-500: #d946ef;
  --color-accent-600: #c026d3;
  --color-accent-700: #a21caf;
  --color-accent-800: #86198f;
  --color-accent-900: #701a75;
  --color-accent-950: #4a044e;

  --font-family-sans: "Inter", system-ui, sans-serif;

  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.5s ease-out;
  --animate-bounce-subtle: bounceSubtle 2s infinite;
  --animate-gradient: gradient 15s ease infinite;

  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --shadow-glass-inset: inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
  --shadow-elegant: 0 4px 20px -2px rgba(0, 0, 0, 0.1);
  --shadow-elegant-lg: 0 10px 40px -4px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceSubtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes gradient {
  0%,
  100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@layer base {
  html {
    font-family: "Inter", system-ui, sans-serif;
  }

  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  .glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card-dark {
    background: rgba(17, 24, 39, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(75, 85, 99, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .btn-primary {
    background: linear-gradient(to right, #2563eb, #1d4ed8);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s;
    transform: scale(1);
    border: none;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: linear-gradient(to right, #1d4ed8, #1e40af);
    transform: scale(1.05);
  }

  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
  }

  .btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: scale(1);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #374151;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: #f9fafb;
  }

  .btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(107, 114, 128, 0.2);
  }

  .input-modern {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(229, 231, 235, 0.5);
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    transition: all 0.2s;
  }

  .input-modern:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
  }

  .input-modern::placeholder {
    color: #9ca3af;
  }

  .card-modern {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(229, 231, 235, 0.5);
    box-shadow: 0 4px 20px -2px rgba(0, 0, 0, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.2s;
  }

  .card-modern:hover {
    transform: scale(1.02);
    box-shadow: 0 10px 40px -4px rgba(0, 0, 0, 0.1);
  }
}
