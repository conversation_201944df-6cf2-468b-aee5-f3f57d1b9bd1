from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import User, Product, Order, Store
from routers.auth import get_current_user

router = APIRouter()


@router.get("/products/{product_id}/orders")
async def get_orders_for_product(
    product_id: int, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    product = db.query(Product).join(Store).filter(Product.id == product_id, Store.owner_id == current_user.id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    orders = db.query(Order).filter(Order.store_id == product.store_id).all()
    import json as _json

    result = []
    for order in orders:
        try:
            line_items = _json.loads(order.line_items) if order.line_items else []
            for item in line_items:
                variant = item.get("variant") if item else None
                product_obj = variant.get("product") if variant else None
                product_gid = product_obj.get("id") if product_obj else None
                ext_id = None
                if product_gid:
                    import re

                    match = re.search(r"/Product/(\d+)", product_gid)
                    if match:
                        ext_id = match.group(1)
                if ext_id and str(product.external_id).strip() == ext_id:
                    result.append(
                        {
                            "id": order.id,
                            "order_number": order.order_number,
                            "customer_email": order.customer_email,
                            "total_price": order.total_price,
                            "status": order.status,
                            "order_date": (
                                order.order_date.isoformat()
                                if hasattr(order.order_date, "isoformat")
                                else str(order.order_date)
                            ),
                        }
                    )
                    break
        except Exception as e:
            continue
    return result
