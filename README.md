# E-commerce Integration Hub

A comprehensive web application for managing multiple e-commerce stores (Shopify and WooCommerce) from a single dashboard. Built with React, FastAPI, and deployed on Google Cloud Platform.

## 🚀 Features

- **Multi-Store Management**: Connect and manage multiple Shopify and WooCommerce stores
- **Product Synchronization**: Automatically sync products and inventory across platforms
- **Order Management**: Track and manage orders from all connected stores
- **Real-time Analytics**: Get insights and analytics across all platforms
- **Secure Authentication**: JWT-based authentication with user management
- **Cloud-Native**: Deployed on GCP with auto-scaling and high availability

## 🏗️ Architecture

- **Frontend**: React with TypeScript, Tailwind CSS, Vite
- **Backend**: FastAPI with Python, SQLAlchemy, PostgreSQL
- **Infrastructure**: Google Cloud Platform (Cloud Run, Cloud SQL, Secret Manager)
- **Authentication**: JWT tokens with secure password hashing
- **APIs**: Shopify Admin API, WooCommerce REST API

## 📋 Prerequisites

- Node.js 18+ and npm
- Python 3.11+
- Docker
- Google Cloud CLI
- Terraform
- Git

## 🛠️ Local Development Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd ecommerce-hub
```

### 2. Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Set up database (PostgreSQL)
# Create database: ecommerce_db
# Update DATABASE_URL in .env

# Run migrations
alembic upgrade head

# Start the server
uvicorn main:app --reload
```

### 3. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your API URL

# Start development server
npm run dev
```

### 4. Access the Application

- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 🚀 Production Deployment

### 1. GCP Setup

```bash
# Set your GCP project ID
export PROJECT_ID="your-gcp-project-id"

# Enable required APIs
gcloud services enable cloudsql.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable secretmanager.googleapis.com
```

### 2. Infrastructure Deployment

```bash
cd infrastructure

# Copy and configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy infrastructure
terraform init
terraform plan
terraform apply
```

### 3. Application Deployment

```bash
# Use the deployment script
./deploy.sh your-gcp-project-id us-central1
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```
DATABASE_URL=postgresql://user:password@localhost/ecommerce_db
SECRET_KEY=your-secret-key
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
```

#### Frontend (.env)
```
VITE_API_URL=http://localhost:8000
```

## 📚 API Documentation

The API documentation is automatically generated and available at:
- Local: http://localhost:8000/docs
- Production: https://your-api-url/docs

### Key Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/stores/` - Get user's stores
- `POST /api/stores/` - Connect new store
- `GET /api/products/` - Get products
- `GET /api/orders/` - Get orders

## 🛍️ E-commerce Platform Setup

### Shopify Setup

1. **Create Shopify Partner Account**
   - Go to https://partners.shopify.com/
   - Sign up for a partner account
   - Create a development store

2. **Create Private App**
   - In your development store, go to Apps > App and sales channel settings
   - Click "Develop apps for your store"
   - Create a new app with required permissions:
     - Products: Read access
     - Orders: Read access
     - Inventory: Read access

3. **Get API Credentials**
   - Copy the API key and API secret key
   - Use these in the store connection form

### WooCommerce Setup

1. **Set up WooCommerce Site**
   - Install WordPress
   - Install WooCommerce plugin
   - Complete the setup wizard

2. **Generate API Keys**
   - Go to WooCommerce > Settings > Advanced > REST API
   - Click "Add key"
   - Set permissions to Read/Write
   - Copy the Consumer Key and Consumer Secret

3. **Test Connection**
   - Use the API credentials in the store connection form
   - Test the connection before saving

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 📊 Monitoring and Logging

- **Application Logs**: Available in Google Cloud Logging
- **Performance Monitoring**: Cloud Run metrics
- **Database Monitoring**: Cloud SQL insights
- **Error Tracking**: Integrated with Cloud Error Reporting

## 🔒 Security

- JWT-based authentication
- Password hashing with bcrypt
- HTTPS enforcement in production
- CORS configuration
- SQL injection protection with SQLAlchemy
- Input validation with Pydantic

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API docs at `/docs`

## 🗺️ Roadmap

- [ ] Multi-currency support
- [ ] Advanced analytics dashboard
- [ ] Automated inventory management
- [ ] Email notifications
- [ ] Mobile app
- [ ] Additional platform integrations (BigCommerce, Magento)
