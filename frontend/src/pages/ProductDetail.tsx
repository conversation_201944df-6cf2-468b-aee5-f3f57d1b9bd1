import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Package,
  TrendingUp,
  ExternalLink,
  Tag,
  DollarSign,
  BarChart3,
  ShoppingCart,
} from "lucide-react";
import { api } from "../services/api";
import SalesForecastChart from "../components/SalesForecastChart";
import { useScroll } from "../contexts/ScrollContext";

interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  description_html?: string;
  handle?: string;
  price: number;
  compare_at_price?: number;
  inventory_quantity: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  weight_unit?: string;
  status: string;
  product_type?: string;
  vendor?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  store_id: number;
  order_count?: number;
}

interface Order {
  id: number;
  external_id: string;
  order_number: string;
  customer_email?: string;
  total_price: number;
  status: string;
  financial_status: string;
  fulfillment_status: string;
  order_date: string;
  line_items: any[];
}

interface ForecastData {
  date: string;
  predicted_sales: number;
  lower_bound: number;
  upper_bound: number;
}

interface ModelForecast {
  model_name: string;
  forecast_data: ForecastData[];
  model_info: string;
}

interface AnomalyData {
  date: string;
  value: number;
  detector: string;
  type: string;
}

interface ForecastResult {
  success: boolean;
  message?: string;
  forecast_data?: ForecastData[]; // Legacy single model support
  forecasts?: Record<string, ModelForecast>; // Multi-model forecasts
  ensemble_forecast?: ForecastData[]; // Ensemble forecast
  anomalies?: AnomalyData[]; // Anomaly detection results
  historical_data?: any[];
  forecast_period?: string;
  available_models?: string[];
  product_info?: {
    id: number;
    title: string;
    sku?: string;
    current_inventory: number;
  };
}

const ProductDetail: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const { scrollToTop } = useScroll();

  const [product, setProduct] = useState<Product | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [forecastData, setForecastData] = useState<ForecastResult | null>(null);
  const [loading, setLoading] = useState(true); // Only for product data
  const [ordersLoading, setOrdersLoading] = useState(true); // Separate loading for orders
  const [forecastLoading, setForecastLoading] = useState(false); // Separate loading for forecast
  const [selectedForecastPeriod, setSelectedForecastPeriod] = useState("1M");
  const [imageError, setImageError] = useState(false);

  const loadProduct = useCallback(async () => {
    try {
      const response = await api.get(`/api/products/${productId}`);
      setProduct(response.data);
    } catch (error) {
      console.error("Failed to load product:", error);
    } finally {
      setLoading(false);
    }
  }, [productId]);

  const loadOrders = useCallback(async () => {
    if (!productId) return;

    setOrdersLoading(true);
    try {
      const response = await api.get(`/api/products/${productId}/orders`);
      setOrders(response.data);
    } catch (error) {
      console.error("Failed to load orders:", error);
    } finally {
      setOrdersLoading(false);
    }
  }, [productId]);

  const loadForecast = useCallback(async () => {
    setForecastLoading(true);
    try {
      const response = await api.get(`/api/products/${productId}/forecast`, {
        params: { forecast_period: selectedForecastPeriod },
      });
      setForecastData(response.data);
    } catch (error) {
      console.error("Failed to load forecast:", error);
      setForecastData({
        success: false,
        message: "Failed to load forecast data",
      });
    } finally {
      setForecastLoading(false);
    }
  }, [productId, selectedForecastPeriod]);

  const handleRefreshForecast = () => {
    loadForecast();
  };

  const handlePopulateSalesData = async () => {
    try {
      await api.post("/api/products/populate-sales-data");
      loadForecast();
    } catch (error) {
      console.error("Failed to populate sales data:", error);
    }
  };

  // Effects - Load product and orders immediately, forecast in parallel
  useEffect(() => {
    if (productId) {
      // Scroll to top when entering product detail page
      scrollToTop();

      // Load product and orders immediately (fast operations)
      loadProduct();
      loadOrders();

      // Load forecast in parallel (slower operation)
      // Small delay to let the page render first
      setTimeout(() => {
        loadForecast();
      }, 100);
    }
  }, [productId, scrollToTop, loadProduct, loadOrders, loadForecast]);

  useEffect(() => {
    if (productId) {
      loadForecast();
    }
  }, [selectedForecastPeriod, loadForecast, productId]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      case "archived":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            Product not found
          </h3>
          <button
            onClick={() => navigate("/products")}
            className="text-blue-500 hover:text-blue-600"
          >
            Back to Products
          </button>
        </div>
      </div>
    );
  }

  const primaryImage =
    product.images && product.images.length > 0 ? product.images[0] : null;
  const hasDiscount =
    product.compare_at_price && product.compare_at_price > product.price;
  const discountPercentage = hasDiscount
    ? Math.round(
        ((product.compare_at_price! - product.price) /
          product.compare_at_price!) *
          100
      )
    : 0;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate("/products")}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft size={20} className="mr-2" />
                Back to Products
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-semibold text-gray-900 truncate max-w-md">
                {product.title}
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                  product.status
                )}`}
              >
                {product.status}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Product Info */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Product Image */}
              <div className="relative h-64 bg-gray-100">
                {primaryImage && !imageError ? (
                  <img
                    src={primaryImage}
                    alt={product.title}
                    className="w-full h-full object-cover"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                    <Package className="text-gray-400" size={64} />
                  </div>
                )}
              </div>

              {/* Product Details */}
              <div className="p-6">
                <div className="space-y-4">
                  {/* Price */}
                  <div className="flex items-center space-x-2">
                    <DollarSign className="text-green-600" size={20} />
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold text-gray-900">
                        ${product.price.toFixed(2)}
                      </span>
                      {hasDiscount && (
                        <>
                          <span className="text-lg text-gray-500 line-through">
                            ${product.compare_at_price!.toFixed(2)}
                          </span>
                          <span className="bg-red-100 text-red-800 text-sm px-2 py-1 rounded">
                            {discountPercentage}% off
                          </span>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Inventory */}
                  <div className="flex items-center space-x-2">
                    <Package className="text-blue-600" size={20} />
                    <span className="text-gray-700">
                      <span className="font-medium">
                        {product.inventory_quantity}
                      </span>{" "}
                      in stock
                    </span>
                  </div>

                  {/* SKU */}
                  {product.sku && (
                    <div className="flex items-center space-x-2">
                      <Tag className="text-purple-600" size={20} />
                      <span className="text-gray-700">
                        SKU: <span className="font-mono">{product.sku}</span>
                      </span>
                    </div>
                  )}

                  {/* Product Type */}
                  {product.product_type && (
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="text-orange-600" size={20} />
                      <span className="text-gray-700">
                        {product.product_type}
                      </span>
                    </div>
                  )}

                  {/* Vendor */}
                  {product.vendor && (
                    <div className="flex items-center space-x-2">
                      <ShoppingCart className="text-indigo-600" size={20} />
                      <span className="text-gray-700">{product.vendor}</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="mt-6 space-y-3">
                  {product.handle && (
                    <button
                      onClick={() =>
                        window.open(
                          `https://shopify.com/admin/products/${product.external_id}`,
                          "_blank"
                        )
                      }
                      className="w-full bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 transition-colors font-medium flex items-center justify-center space-x-2"
                    >
                      <ExternalLink size={18} />
                      <span>View in Shopify</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Forecast Chart */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <TrendingUp className="mr-2 text-blue-600" size={24} />
                  Sales Forecast
                </h2>

                {/* Forecast Period Selector */}
                <div className="flex space-x-2">
                  {["5Y", "2Y", "1Y", "6M", "3M", "1M"].map((period) => (
                    <button
                      key={period}
                      onClick={() => setSelectedForecastPeriod(period)}
                      className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                        selectedForecastPeriod === period
                          ? "bg-blue-500 text-white"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      }`}
                    >
                      {period}
                    </button>
                  ))}
                </div>
              </div>

              {forecastData?.success && forecastData.product_info ? (
                <SalesForecastChart
                  forecastData={forecastData.forecast_data || []}
                  forecasts={forecastData.forecasts || {}}
                  ensembleForecast={forecastData.ensemble_forecast || []}
                  anomalies={forecastData.anomalies || []}
                  historicalData={forecastData.historical_data || []}
                  productInfo={forecastData.product_info}
                  isLoading={forecastLoading}
                  onRefresh={handleRefreshForecast}
                  onPopulateSalesData={handlePopulateSalesData}
                  forecastPeriod={selectedForecastPeriod}
                  availableModels={forecastData.available_models || []}
                />
              ) : (
                <SalesForecastChart
                  forecastData={[]}
                  forecasts={{}}
                  ensembleForecast={[]}
                  anomalies={[]}
                  historicalData={[]}
                  productInfo={{
                    id: product.id,
                    title: product.title,
                    sku: product.sku,
                    current_inventory: product.inventory_quantity,
                  }}
                  isLoading={forecastLoading}
                  error={forecastData?.message}
                  onRefresh={handleRefreshForecast}
                  onPopulateSalesData={handlePopulateSalesData}
                  forecastPeriod={selectedForecastPeriod}
                  availableModels={[]}
                />
              )}
            </div>
          </div>
        </div>

        {/* Orders List - Below the forecast chart */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <ShoppingCart className="mr-2 text-green-600" size={24} />
                Recent Orders
                {ordersLoading && (
                  <div className="ml-3 animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                )}
              </h2>
            </div>

            {ordersLoading ? (
              // Orders Loading Skeleton
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {[...Array(5)].map((_, index) => (
                      <tr key={index} className="animate-pulse">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="h-4 bg-gray-200 rounded w-16"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="h-4 bg-gray-200 rounded w-32"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="h-4 bg-gray-200 rounded w-20"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="h-4 bg-gray-200 rounded w-24"></div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : orders.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orders.map((order) => (
                      <tr
                        key={order.id}
                        className="hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => navigate(`/orders/${order.id}`)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            #{order.order_number}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {order.customer_email || "Guest"}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-green-600">
                            ${order.total_price.toFixed(2)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              order.status === "fulfilled"
                                ? "bg-green-100 text-green-800"
                                : order.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {order.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(order.order_date).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <ShoppingCart
                  className="mx-auto text-gray-400 mb-4"
                  size={48}
                />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No orders found
                </h3>
                <p className="text-gray-500">
                  No orders found for this product.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
