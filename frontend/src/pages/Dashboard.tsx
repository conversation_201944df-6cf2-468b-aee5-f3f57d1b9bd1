import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/useAuth";
import { Store, Package, ShoppingCart, Plus } from "lucide-react";
import { api } from "../services/api";

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [productCount, setProductCount] = useState<number>(0);
  const [orderCount, setOrderCount] = useState<number>(0);
  const [revenue, setRevenue] = useState<number>(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch products
        const productsRes = await api.get("/api/products/");
        setProductCount(
          Array.isArray(productsRes.data) ? productsRes.data.length : 0
        );

        // Fetch orders
        const ordersRes = await api.get("/api/orders/");
        const orders = Array.isArray(ordersRes.data) ? ordersRes.data : [];
        setOrderCount(orders.length);

        // Calculate revenue for current month
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        const monthRevenue = orders.reduce(
          (
            sum: number,
            order: { order_date: string; total_price: number | string }
          ) => {
            const orderDate = new Date(order.order_date);
            if (
              orderDate.getMonth() === currentMonth &&
              orderDate.getFullYear() === currentYear
            ) {
              return (
                sum +
                (typeof order.total_price === "string"
                  ? parseFloat(order.total_price)
                  : order.total_price || 0)
              );
            }
            return sum;
          },
          0
        );
        setRevenue(monthRevenue);
      } catch {
        setProductCount(0);
        setOrderCount(0);
        setRevenue(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.full_name}!
        </h1>
        <p className="text-gray-600">
          Manage your e-commerce stores and track your business performance.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Link
          to="/stores"
          className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center justify-between mb-4">
            <Store className="text-blue-500" size={32} />
            <Plus className="text-gray-400" size={20} />
          </div>
          <h3 className="text-lg font-semibold mb-2">Connect Store</h3>
          <p className="text-gray-600 text-sm">
            Add your Shopify or WooCommerce store
          </p>
        </Link>

        <Link
          to="/products"
          className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center justify-between mb-4">
            <Package className="text-green-500" size={32} />
            <span className="text-2xl font-bold text-gray-900">
              {loading ? "-" : productCount}
            </span>
          </div>
          <h3 className="text-lg font-semibold mb-2">Products</h3>
          <p className="text-gray-600 text-sm">Manage your product catalog</p>
        </Link>

        <Link
          to="/orders"
          className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center justify-between mb-4">
            <ShoppingCart className="text-purple-500" size={32} />
            <span className="text-2xl font-bold text-gray-900">
              {loading ? "-" : orderCount}
            </span>
          </div>
          <h3 className="text-lg font-semibold mb-2">Orders</h3>
          <p className="text-gray-600 text-sm">Track your recent orders</p>
        </Link>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between mb-4">
            <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">$</span>
            </div>
            <span className="text-2xl font-bold text-gray-900">
              {loading
                ? "-"
                : `$${revenue.toLocaleString(undefined, {
                    maximumFractionDigits: 2,
                  })}`}
            </span>
          </div>
          <h3 className="text-lg font-semibold mb-2">Revenue</h3>
          <p className="text-gray-600 text-sm">Total sales this month</p>
        </div>
      </div>

      {/* Getting Started */}
      <div className="bg-blue-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Getting Started
        </h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
              1
            </div>
            <div>
              <h3 className="font-semibold mb-1">Connect Your Store</h3>
              <p className="text-gray-600 text-sm">
                Add your Shopify or WooCommerce store using API credentials
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
              2
            </div>
            <div>
              <h3 className="font-semibold mb-1">Sync Your Data</h3>
              <p className="text-gray-600 text-sm">
                Import products, inventory, and order history
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
              3
            </div>
            <div>
              <h3 className="font-semibold mb-1">Monitor & Manage</h3>
              <p className="text-gray-600 text-sm">
                Track performance and manage your business
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
