#!/usr/bin/env python3
"""
Debug the actual sales data to see if predictions are reasonable
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models import SalesData, Product
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def debug_real_sales():
    """Debug the actual sales data for product 1"""
    
    print("🔍 Debugging REAL sales data for product 1...")
    
    # Get database session
    db = next(get_db())
    
    # Get product 1
    product = db.query(Product).filter(Product.id == 1).first()
    if not product:
        print("❌ Product 1 not found")
        return
    
    print(f"📦 Product: {product.title} (ID: {product.id})")
    
    # Get all sales data for product 1
    sales_records = (
        db.query(SalesData)
        .filter(SalesData.product_id == 1)
        .order_by(SalesData.date.desc())  # Most recent first
        .all()
    )
    
    if not sales_records:
        print("❌ No sales data found for product 1")
        return
    
    print(f"📊 Total sales records: {len(sales_records)}")
    
    # Convert to list for analysis
    sales_data = [
        {
            "date": record.date,
            "quantity_sold": record.quantity_sold,
            "revenue": record.revenue
        }
        for record in sales_records
    ]
    
    # Create DataFrame
    df = pd.DataFrame(sales_data)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date')
    
    print(f"\n📈 Sales Data Overview:")
    print(f"  Date range: {df['date'].min().date()} to {df['date'].max().date()}")
    print(f"  Total days with data: {len(df)}")
    
    print(f"\n📊 Quantity Statistics:")
    print(f"  Min: {df['quantity_sold'].min()}")
    print(f"  Max: {df['quantity_sold'].max()}")
    print(f"  Mean: {df['quantity_sold'].mean():.2f}")
    print(f"  Median: {df['quantity_sold'].median():.2f}")
    print(f"  Std: {df['quantity_sold'].std():.2f}")
    
    # Show recent sales (last 30 days)
    recent_cutoff = df['date'].max() - timedelta(days=30)
    recent_sales = df[df['date'] >= recent_cutoff]
    
    print(f"\n📅 Recent Sales (Last 30 days): {len(recent_sales)} records")
    if len(recent_sales) > 0:
        print(f"  Recent Mean: {recent_sales['quantity_sold'].mean():.2f}")
        print(f"  Recent Median: {recent_sales['quantity_sold'].median():.2f}")
        print(f"  Recent Max: {recent_sales['quantity_sold'].max()}")
        print(f"  Recent Min: {recent_sales['quantity_sold'].min()}")
    
    # Show last 10 sales records
    print(f"\n📋 Last 10 Sales Records:")
    print("Date\t\tQuantity\tRevenue")
    print("-" * 40)
    for _, row in df.tail(10).iterrows():
        print(f"{row['date'].date()}\t{row['quantity_sold']}\t\t${row['revenue']:.2f}")
    
    # Show zero sales days
    zero_sales = df[df['quantity_sold'] == 0]
    print(f"\n🔍 Zero Sales Days: {len(zero_sales)}/{len(df)} ({len(zero_sales)/len(df)*100:.1f}%)")
    
    # Show non-zero sales statistics
    non_zero_sales = df[df['quantity_sold'] > 0]
    if len(non_zero_sales) > 0:
        print(f"\n📈 Non-Zero Sales Days: {len(non_zero_sales)}")
        print(f"  Non-zero Mean: {non_zero_sales['quantity_sold'].mean():.2f}")
        print(f"  Non-zero Median: {non_zero_sales['quantity_sold'].median():.2f}")
    
    # Compare with current predictions
    print(f"\n🔮 Current Model Predictions vs Reality:")
    print(f"  Actual recent average: {recent_sales['quantity_sold'].mean():.2f} units/day")
    print(f"  Actual overall average: {df['quantity_sold'].mean():.2f} units/day")
    print(f"  Prophet prediction: ~5.50 units/day")
    print(f"  FB_Prophet prediction: ~7.27 units/day")
    
    # Analysis
    recent_avg = recent_sales['quantity_sold'].mean() if len(recent_sales) > 0 else df['quantity_sold'].mean()
    overall_avg = df['quantity_sold'].mean()
    
    print(f"\n🎯 Prediction Analysis:")
    if recent_avg < 2:
        print(f"  ❌ Predictions (5-7) are TOO HIGH compared to recent average ({recent_avg:.2f})")
    elif recent_avg > 8:
        print(f"  ❌ Predictions (5-7) are TOO LOW compared to recent average ({recent_avg:.2f})")
    else:
        print(f"  ✅ Predictions (5-7) seem reasonable compared to recent average ({recent_avg:.2f})")
    
    print(f"\n💡 Insights:")
    print(f"  - If recent sales are much lower than predictions, the models may be overfitting to historical peaks")
    print(f"  - If recent sales are similar to predictions, the models are working correctly")
    print(f"  - Zero sales days: {len(zero_sales)/len(df)*100:.1f}% suggests sparse/intermittent sales pattern")

if __name__ == "__main__":
    debug_real_sales()
