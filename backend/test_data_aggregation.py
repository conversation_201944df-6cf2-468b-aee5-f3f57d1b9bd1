#!/usr/bin/env python3
"""
Test what data the models are actually receiving
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from services.forecasting_service import SalesForecastingService
import pandas as pd
from collections import defaultdict

def test_data_aggregation():
    """Test what data the models are actually receiving"""
    
    print("🔍 Testing Data Aggregation...")
    print("=" * 60)
    
    # Get database session
    db = next(get_db())
    
    try:
        # Initialize forecasting service
        service = SalesForecastingService(db)
        
        # Get raw sales data (what the models start with)
        raw_sales_data = service._get_historical_sales_data(1)
        print(f"📊 Raw Sales Data:")
        print(f"  Total records: {len(raw_sales_data)}")
        
        if raw_sales_data:
            # Show first few records
            print(f"  First 5 records:")
            for i, record in enumerate(raw_sales_data[:5]):
                print(f"    {record['date']}: {record['quantity_sold']} units")
            
            # Group by date to see what aggregation should produce
            daily_totals = defaultdict(int)
            for record in raw_sales_data:
                date_str = str(record['date'])
                daily_totals[date_str] += record['quantity_sold']
            
            # Sort by date
            sorted_dates = sorted(daily_totals.keys())
            
            print(f"\n📅 Expected Daily Aggregation:")
            print(f"  Unique dates: {len(sorted_dates)}")
            print(f"  Date range: {sorted_dates[0]} to {sorted_dates[-1]}")
            
            # Show first few aggregated days
            print(f"  First 10 aggregated days:")
            for date in sorted_dates[:10]:
                total = daily_totals[date]
                print(f"    {date}: {total} units/day")
            
            # Show high sales days
            high_days = [(date, total) for date, total in daily_totals.items() if total > 5]
            if high_days:
                print(f"\n🔥 High Sales Days (>5 units):")
                for date, total in sorted(high_days, key=lambda x: x[1], reverse=True)[:5]:
                    print(f"    {date}: {total} units/day")
            
            # Calculate statistics
            all_daily_totals = list(daily_totals.values())
            avg_daily = sum(all_daily_totals) / len(all_daily_totals)
            max_daily = max(all_daily_totals)
            min_daily = min(all_daily_totals)
            
            print(f"\n📈 Expected Statistics:")
            print(f"  Average daily sales: {avg_daily:.2f} units/day")
            print(f"  Range: {min_daily} to {max_daily} units/day")
            print(f"  Days with >10 units: {sum(1 for x in all_daily_totals if x > 10)}")
            
            # Now test what the Darts preprocessing actually produces
            print(f"\n🔧 Testing Darts Preprocessing...")
            try:
                ts = service._prepare_darts_data(raw_sales_data)

                # Use the correct Darts API
                ts_df = ts.pd_dataframe()  # This should work in newer Darts versions

                print(f"  Darts TimeSeries shape: {ts_df.shape}")
                print(f"  Darts data range: {ts_df.iloc[:, 0].min():.3f} to {ts_df.iloc[:, 0].max():.3f}")
                print(f"  Darts data mean: {ts_df.iloc[:, 0].mean():.3f}")
                print(f"  Days with >5 units: {sum(1 for x in ts_df.iloc[:, 0] if x > 5)}")

                # Show first few values
                print(f"  First 10 Darts values:")
                for i in range(min(10, len(ts_df))):
                    date = ts_df.index[i].strftime('%Y-%m-%d')
                    value = ts_df.iloc[i, 0]
                    print(f"    {date}: {value:.3f}")

            except AttributeError as e:
                # Try alternative API
                try:
                    ts_values = ts.values()
                    ts_times = ts.time_index

                    print(f"  Darts TimeSeries length: {len(ts_values)}")
                    print(f"  Darts data range: {ts_values.min():.3f} to {ts_values.max():.3f}")
                    print(f"  Darts data mean: {ts_values.mean():.3f}")
                    print(f"  Days with >5 units: {sum(1 for x in ts_values.flatten() if x > 5)}")

                    # Show first few values
                    print(f"  First 10 Darts values:")
                    for i in range(min(10, len(ts_values))):
                        date = ts_times[i].strftime('%Y-%m-%d')
                        value = ts_values[i][0] if len(ts_values.shape) > 1 else ts_values[i]
                        print(f"    {date}: {value:.3f}")

                except Exception as e2:
                    print(f"  ❌ Darts preprocessing failed: {e} / {e2}")
            except Exception as e:
                print(f"  ❌ Darts preprocessing failed: {e}")
            
            print(f"\n✅ Analysis:")
            if len(high_days) > 0:
                print(f"  Expected high sales days: {len(high_days)}")
                print(f"  Expected average: {avg_daily:.2f} units/day")
                print(f"  If models predict ~2 units/day, there's a preprocessing issue")
                print(f"  If models predict ~7 units/day, preprocessing is working correctly")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_data_aggregation()
