#!/usr/bin/env python3
"""
Debug script to compare the exact data being fed to both Prophet implementations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from services.forecasting_service import ForecastingService
import logging

# Set up logging to see all debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_prophet_data():
    """Debug the exact data being fed to both Prophet implementations"""
    
    # Get database session
    db = next(get_db())
    
    # Create forecasting service
    forecasting_service = ForecastingService(db)
    
    # Test with product 1 (the problematic one)
    product_id = 1
    days_ahead = 7
    
    print(f"🔍 Debugging Prophet data preprocessing for product {product_id}")
    print("=" * 80)
    
    # Get sales data
    from models import SalesData
    sales_data = db.query(SalesData).filter(SalesData.product_id == product_id).all()
    
    if not sales_data:
        print("❌ No sales data found for product 1")
        return
    
    # Convert to list of dicts (same format as the service expects)
    sales_data_list = [
        {
            "date": sale.date.strftime("%Y-%m-%d"),
            "quantity_sold": sale.quantity_sold
        }
        for sale in sales_data
    ]
    
    print(f"📊 Raw sales data: {len(sales_data_list)} records")
    print(f"   Date range: {sales_data_list[0]['date']} to {sales_data_list[-1]['date']}")
    
    # Test Darts preprocessing
    print("\n🔍 Testing Darts Prophet preprocessing...")
    try:
        ts = forecasting_service._preprocess_sales_data(sales_data_list)
        print(f"✅ Darts preprocessing successful")
        print(f"   TimeSeries length: {len(ts)}")
        print(f"   TimeSeries values: min={ts.values().min():.3f}, max={ts.values().max():.3f}, mean={ts.values().mean():.3f}")
    except Exception as e:
        print(f"❌ Darts preprocessing failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test FB_Prophet preprocessing
    print("\n🔍 Testing FB_Prophet preprocessing...")
    try:
        result = forecasting_service._generate_fb_prophet_forecast(sales_data_list, "FB_Prophet", days_ahead)
        if result:
            print(f"✅ FB_Prophet preprocessing and forecast successful")
            print(f"   Forecast data points: {len(result['forecast_data'])}")
            if result['forecast_data']:
                predictions = [point['predicted_sales'] for point in result['forecast_data']]
                print(f"   Predictions: min={min(predictions):.3f}, max={max(predictions):.3f}, mean={sum(predictions)/len(predictions):.3f}")
        else:
            print(f"❌ FB_Prophet preprocessing failed")
    except Exception as e:
        print(f"❌ FB_Prophet preprocessing failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test Darts Prophet forecast for comparison
    print("\n🔍 Testing Darts Prophet forecast...")
    try:
        if 'ts' in locals():
            result = forecasting_service._generate_single_model_forecast(ts, "Prophet", days_ahead)
            if result:
                print(f"✅ Darts Prophet forecast successful")
                print(f"   Forecast data points: {len(result['forecast_data'])}")
                if result['forecast_data']:
                    predictions = [point['predicted_sales'] for point in result['forecast_data']]
                    print(f"   Predictions: min={min(predictions):.3f}, max={max(predictions):.3f}, mean={sum(predictions)/len(predictions):.3f}")
            else:
                print(f"❌ Darts Prophet forecast failed")
    except Exception as e:
        print(f"❌ Darts Prophet forecast failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("🔍 Check the logs above for detailed data comparison between the two implementations")

if __name__ == "__main__":
    debug_prophet_data()
