"""
Shopify GraphQL API Service
Handles all interactions with Shopify Admin and Storefront GraphQL APIs
"""

import httpx
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ShopifyGraphQLService:
    """Service for interacting with Shopify GraphQL APIs"""

    def __init__(self, shop_domain: str, admin_access_token: str, storefront_access_token: Optional[str] = None):
        self.shop_domain = shop_domain
        self.admin_access_token = admin_access_token
        self.storefront_access_token = storefront_access_token

        # Update to a current stable API version
        # Shopify API versions follow a quarterly release schedule (YYYY-MM)
        self.admin_api_version = "2024-01"  # Update to a current stable version
        self.storefront_api_version = "2024-01"  # Update to a current stable version

        # API endpoints
        self.admin_endpoint = f"https://{shop_domain}/admin/api/{self.admin_api_version}/graphql.json"
        self.storefront_endpoint = f"https://{shop_domain}/api/{self.storefront_api_version}/graphql.json"

        logger.info(f"Initialized Shopify service for {shop_domain} with Admin API version {self.admin_api_version}")

    async def test_admin_connection(self) -> Dict[str, Any]:
        """Test connection to Shopify Admin API"""
        query = """
        query {
            shop {
                id
                name
                email
                myshopifyDomain
                primaryDomain {
                    url
                    host
                }
                plan {
                    displayName
                }
                currencyCode
            }
        }
        """

        try:
            result = await self._execute_admin_query(query)
            if result.get("data", {}).get("shop"):
                return {
                    "success": True,
                    "message": "Successfully connected to Shopify Admin API",
                    "shop_info": result["data"]["shop"],
                }
            else:
                errors = result.get("errors", [])
                error_messages = [e.get("message", "Unknown error") for e in errors]
                return {
                    "success": False,
                    "message": f"Failed to retrieve shop information: {'; '.join(error_messages)}",
                    "errors": errors,
                }
        except Exception as e:
            logger.error(f"Admin API connection test failed: {str(e)}")
            return {"success": False, "message": f"Connection failed: {str(e)}"}

    async def test_storefront_connection(self) -> Dict[str, Any]:
        """Test connection to Shopify Storefront API"""
        if not self.storefront_access_token:
            return {"success": False, "message": "Storefront access token not provided"}

        query = """
        query {
            shop {
                name
                primaryDomain {
                    host
                }
            }
        }
        """

        try:
            result = await self._execute_storefront_query(query)
            if result.get("data", {}).get("shop"):
                return {
                    "success": True,
                    "message": "Successfully connected to Shopify Storefront API",
                    "shop_info": result["data"]["shop"],
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to retrieve shop information from Storefront API",
                    "errors": result.get("errors", []),
                }
        except Exception as e:
            logger.error(f"Storefront API connection test failed: {str(e)}")
            return {"success": False, "message": f"Storefront connection failed: {str(e)}"}

    async def get_shop_info(self) -> Dict[str, Any]:
        """Get comprehensive shop information"""
        query = """
        query {
            shop {
                id
                name
                email
                myshopifyDomain
                primaryDomain {
                    url
                    host
                }
                plan {
                    displayName
                }
                currencyCode
                timezoneAbbreviation
                weightUnit
                billingAddress {
                    country
                    countryCodeV2
                }
            }
        }
        """

        result = await self._execute_admin_query(query)
        return result.get("data", {}).get("shop", {})

    async def get_products(self, limit: int = 50, cursor: Optional[str] = None) -> Dict[str, Any]:
        """Get products using GraphQL Admin API"""
        after_clause = f', after: "{cursor}"' if cursor else ""

        query = f"""
        query {{
            products(first: {limit}{after_clause}) {{
                edges {{
                    node {{
                        id
                        title
                        description
                        descriptionHtml
                        handle
                        productType
                        vendor
                        status
                        tags
                        createdAt
                        updatedAt
                        images(first: 10) {{
                            edges {{
                                node {{
                                    id
                                    url
                                    altText
                                }}
                            }}
                        }}
                        variants(first: 10) {{
                            edges {{
                                node {{
                                    id
                                    title
                                    price
                                    compareAtPrice
                                    sku
                                    barcode
                                    inventoryQuantity
                                    weight
                                    weightUnit
                                }}
                            }}
                        }}
                    }}
                    cursor
                }}
                pageInfo {{
                    hasNextPage
                    endCursor
                }}
            }}
        }}
        """

        result = await self._execute_admin_query(query)

        # Check if there are any errors in the result
        if "errors" in result:
            logger.error(f"GraphQL errors in get_products: {json.dumps(result.get('errors'))}")
            return {"errors": result.get("errors"), "edges": []}

        products = result.get("data", {}).get("products", {})

        # Log product count for debugging
        if "edges" in products:
            logger.info(f"Retrieved {len(products['edges'])} products from Shopify API")
        else:
            logger.warning("No 'edges' found in products response")
            logger.debug(f"Full products response: {json.dumps(products)}")

        return products

    async def get_orders(self, limit: int = 50, cursor: Optional[str] = None) -> Dict[str, Any]:
        """Get orders using GraphQL Admin API"""
        after_clause = f', after: "{cursor}"' if cursor else ""

        query = f"""
        query {{
            orders(first: {limit}{after_clause}) {{
                edges {{
                    node {{
                        id
                        name
                        email
                        createdAt
                        updatedAt
                        totalPriceSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        subtotalPriceSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        totalTaxSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        totalDiscountsSet {{
                            shopMoney {{
                                amount
                                currencyCode
                            }}
                        }}
                        displayFinancialStatus
                        displayFulfillmentStatus
                        customer {{
                            id
                            email
                        }}
                        lineItems(first: 50) {{
                            edges {{
                                node {{
                                    id
                                    title
                                    quantity
                                    variant {{
                                        id
                                        product {{
                                            id
                                        }}
                                    }}
                                    originalTotalSet {{
                                        shopMoney {{
                                            amount
                                            currencyCode
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                    cursor
                }}
                pageInfo {{
                    hasNextPage
                    endCursor
                }}
            }}
        }}
        """

        result = await self._execute_admin_query(query)
        return result.get("data", {}).get("orders", {})

    async def create_order(self, order_data: dict) -> dict:
        """Create an order using Shopify Admin GraphQL API"""
        mutation = """
        mutation orderCreate($input: OrderInput!) {
            orderCreate(input: $input) {
                order {
                    id
                    name
                    email
                    createdAt
                }
                userErrors {
                    field
                    message
                }
            }
        }
        """
        # Map order_data to Shopify's OrderInput
        input_data = {
            "email": order_data["customer"]["email"],
            "lineItems": [
                {
                    "title": item["title"],
                    "quantity": item["quantity"],
                    "originalUnitPrice": str(item["price"]),
                }
                for item in order_data["line_items"]
            ],
            "customer": {
                "firstName": order_data["customer"]["first_name"],
                "lastName": order_data["customer"]["last_name"],
                "email": order_data["customer"]["email"],
            },
            "financialStatus": order_data.get("financial_status", "paid"),
            "processedAt": order_data.get("processed_at"),
        }
        variables = {"input": input_data}
        result = await self._execute_admin_query(mutation, variables)
        return result

    async def _execute_admin_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a GraphQL query against the Admin API"""
        headers = {"Content-Type": "application/json", "X-Shopify-Access-Token": self.admin_access_token}

        payload = {"query": query}
        if variables:
            payload["variables"] = variables

        try:
            async with httpx.AsyncClient() as client:
                logger.debug(f"Sending request to {self.admin_endpoint} with payload: {json.dumps(payload)}")
                response = await client.post(self.admin_endpoint, headers=headers, json=payload, timeout=30.0)

                # Log response status and headers for debugging
                logger.debug(f"Response status: {response.status_code}")
                logger.debug(f"Response headers: {dict(response.headers)}")

                response.raise_for_status()
                result = response.json()

                # Check for GraphQL errors even with 200 status code
                if "errors" in result:
                    logger.error(f"GraphQL errors: {json.dumps(result['errors'])}")

                return result
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            error_data = {"errors": [{"message": f"HTTP error {e.response.status_code}: {e.response.text}"}]}
            return error_data
        except httpx.RequestError as e:
            logger.error(f"Request error: {str(e)}")
            error_data = {"errors": [{"message": f"Request error: {str(e)}"}]}
            return error_data

    async def _execute_storefront_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a GraphQL query against the Storefront API"""
        if not self.storefront_access_token:
            raise ValueError("Storefront access token is required")

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Storefront-Access-Token": self.storefront_access_token,
        }

        payload = {"query": query}
        if variables:
            payload["variables"] = variables

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(self.storefront_endpoint, headers=headers, json=payload, timeout=30.0)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            error_data = {"errors": [{"message": f"HTTP error {e.response.status_code}: {e.response.text}"}]}
            return error_data
        except httpx.RequestError as e:
            logger.error(f"Request error: {str(e)}")
            error_data = {"errors": [{"message": f"Request error: {str(e)}"}]}
            return error_data
