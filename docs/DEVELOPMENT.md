# Development Guide

This guide covers local development setup, testing, and contribution guidelines for the E-commerce Hub project.

## 🛠️ Development Environment Setup

### Prerequisites

- **Node.js**: 18.x or higher
- **Python**: 3.11 or higher
- **PostgreSQL**: 13.x or higher
- **Git**: Latest version
- **Docker**: For containerized development (optional)

### 1. Repository Setup

```bash
# Clone the repository
git clone <repository-url>
cd ecommerce-hub

# Create development branch
git checkout -b feature/your-feature-name
```

### 2. Backend Development Setup

```bash
cd backend

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install pytest pytest-asyncio black flake8 mypy

# Set up pre-commit hooks (optional)
pip install pre-commit
pre-commit install
```

### 3. Database Setup

```bash
# Install PostgreSQL (Ubuntu/Debian)
sudo apt-get install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE ecommerce_db;
CREATE USER app_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE ecommerce_db TO app_user;
\q

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials
```

### 4. Frontend Development Setup

```bash
cd frontend

# Install dependencies
npm install

# Install development tools
npm install -D @types/node @typescript-eslint/eslint-plugin

# Set up environment
cp .env.example .env
# Edit .env with your API URL
```

## 🏃‍♂️ Running the Application

### Backend Server

```bash
cd backend
source venv/bin/activate

# Run database migrations
alembic upgrade head

# Start development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Alternative docs**: http://localhost:8000/redoc

### Frontend Development Server

```bash
cd frontend

# Start development server
npm run dev
```

The frontend will be available at:
- **Application**: http://localhost:5173

## 🧪 Testing

### Backend Testing

```bash
cd backend

# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test file
pytest tests/test_auth.py

# Run with verbose output
pytest -v
```

### Frontend Testing

```bash
cd frontend

# Run unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run E2E tests (if configured)
npm run test:e2e
```

### Integration Testing

```bash
# Start backend server in test mode
cd backend
pytest tests/integration/

# Test API endpoints
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpass","full_name":"Test User"}'
```

## 📝 Code Style and Standards

### Backend (Python)

```bash
# Format code with Black
black .

# Check code style with flake8
flake8 .

# Type checking with mypy
mypy .

# Sort imports
isort .
```

**Configuration files:**
- `.flake8` - Flake8 configuration
- `pyproject.toml` - Black and other tool configurations

### Frontend (TypeScript/React)

```bash
# Format code with Prettier
npm run format

# Lint code with ESLint
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check
```

**Configuration files:**
- `.eslintrc.js` - ESLint configuration
- `.prettierrc` - Prettier configuration
- `tsconfig.json` - TypeScript configuration

## 🏗️ Project Structure

```
ecommerce-hub/
├── backend/                 # FastAPI backend
│   ├── routers/            # API route handlers
│   ├── models.py           # Database models
│   ├── database.py         # Database configuration
│   ├── config.py           # Application configuration
│   ├── main.py             # FastAPI application
│   └── tests/              # Backend tests
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── contexts/       # React contexts
│   │   └── types/          # TypeScript types
│   └── public/             # Static assets
├── infrastructure/         # Terraform configurations
├── docs/                   # Documentation
└── deploy.sh              # Deployment script
```

## 🔄 Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/store-sync-improvements

# Make changes and commit
git add .
git commit -m "feat: improve store synchronization performance"

# Push branch
git push origin feature/store-sync-improvements

# Create pull request
```

### 2. Database Migrations

```bash
cd backend

# Create new migration
alembic revision --autogenerate -m "Add new column to products table"

# Apply migration
alembic upgrade head

# Rollback migration (if needed)
alembic downgrade -1
```

### 3. Adding New API Endpoints

1. **Create route handler** in `backend/routers/`
2. **Add database models** if needed in `models.py`
3. **Write tests** in `tests/`
4. **Update API documentation** (automatic with FastAPI)
5. **Add frontend service** in `frontend/src/services/`

### 4. Adding New React Components

1. **Create component** in `frontend/src/components/`
2. **Add TypeScript types** in `frontend/src/types/`
3. **Write tests** for the component
4. **Update routing** if it's a page component
5. **Add to Storybook** (if configured)

## 🐛 Debugging

### Backend Debugging

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Use Python debugger
import pdb; pdb.set_trace()

# Use VS Code debugger
# Add breakpoints and use F5 to start debugging
```

### Frontend Debugging

```bash
# Enable React Developer Tools
# Install browser extension

# Use browser debugger
debugger;

# Console logging
console.log('Debug info:', data);

# React strict mode (already enabled in development)
```

### Database Debugging

```bash
# Connect to database
psql -h localhost -U app_user -d ecommerce_db

# View logs
tail -f /var/log/postgresql/postgresql-13-main.log

# Enable query logging in PostgreSQL
# Add to postgresql.conf:
# log_statement = 'all'
```

## 🔧 Environment Variables

### Backend Environment Variables

```bash
# Database
DATABASE_URL=postgresql://app_user:password@localhost/ecommerce_db

# JWT
SECRET_KEY=your-secret-key-for-development
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External APIs
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
WOOCOMMERCE_URL=https://your-test-store.com
WOOCOMMERCE_CONSUMER_KEY=your-consumer-key
WOOCOMMERCE_CONSUMER_SECRET=your-consumer-secret

# Development
ENVIRONMENT=development
LOG_LEVEL=DEBUG
```

### Frontend Environment Variables

```bash
# API Configuration
VITE_API_URL=http://localhost:8000

# Development
VITE_NODE_ENV=development
```

## 📊 Performance Monitoring

### Backend Performance

```bash
# Profile API endpoints
pip install py-spy
py-spy record -o profile.svg -- python -m uvicorn main:app

# Monitor database queries
# Enable slow query logging in PostgreSQL
```

### Frontend Performance

```bash
# Bundle analysis
npm run build
npm run analyze

# Lighthouse audit
npm install -g lighthouse
lighthouse http://localhost:5173
```

## 🚀 Deployment Testing

### Local Docker Testing

```bash
# Build and test backend
cd backend
docker build -t ecommerce-hub-api .
docker run -p 8000:8000 ecommerce-hub-api

# Build and test frontend
cd frontend
docker build -t ecommerce-hub-frontend .
docker run -p 80:80 ecommerce-hub-frontend
```

### Staging Environment

```bash
# Deploy to staging
./deploy.sh your-staging-project-id

# Run integration tests against staging
npm run test:integration:staging
```

## 🤝 Contributing

### Pull Request Process

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes
4. **Add** tests for new functionality
5. **Ensure** all tests pass
6. **Update** documentation
7. **Submit** pull request

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance impact considered
- [ ] Backward compatibility maintained

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

## 📚 Additional Resources

- **FastAPI Documentation**: https://fastapi.tiangolo.com/
- **React Documentation**: https://react.dev/
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/
- **Shopify API Documentation**: https://shopify.dev/api
- **WooCommerce API Documentation**: https://woocommerce.github.io/woocommerce-rest-api-docs/
