import React, { useRef } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import type { ChartOptions, ChartData } from "chart.js";
import { Line } from "react-chartjs-2";
import {
  TrendingUp,
  Calendar,
  Target,
  AlertCircle,
  BarChart3,
} from "lucide-react";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface ForecastData {
  date: string;
  predicted_sales: number;
  lower_bound: number;
  upper_bound: number;
}

interface ModelForecast {
  model_name: string;
  forecast_data: ForecastData[];
  model_info: string;
  mape?: number; // Mean Absolute Percentage Error for model performance
}

interface AnomalyData {
  date: string;
  value: number;
  detector: string;
  type: string;
}

interface ProductInfo {
  id: number;
  title: string;
  sku?: string;
  current_inventory: number;
}

interface HistoricalData {
  date: string;
  actual_sales: number;
  revenue: number;
  type: string;
}

interface SalesForecastChartProps {
  forecastData?: ForecastData[]; // Legacy single model support
  forecasts?: Record<string, ModelForecast>; // Multi-model forecasts
  ensembleForecast?: ForecastData[]; // Ensemble forecast
  anomalies?: AnomalyData[]; // Anomaly detection results
  historicalData?: HistoricalData[];
  productInfo: ProductInfo;
  isLoading?: boolean;
  error?: string;
  onRefresh?: () => void;
  onPopulateSalesData?: () => void;
  forecastPeriod?: string;
  availableModels?: string[];
}

const SalesForecastChart: React.FC<SalesForecastChartProps> = ({
  forecastData = [],
  forecasts = {},
  ensembleForecast = [],
  anomalies = [],
  historicalData = [],
  productInfo,
  isLoading = false,
  error,
  onRefresh,
  onPopulateSalesData,
  forecastPeriod = "1M",
}) => {
  const chartRef = useRef<ChartJS<"line">>(null);

  // State for controlling which models are visible
  const [visibleModels, setVisibleModels] = React.useState<
    Record<string, boolean>
  >(() => {
    const initial: Record<string, boolean> = {};
    Object.keys(forecasts).forEach((model) => {
      initial[model] = true; // Show all models by default
    });
    initial["ensemble"] = true; // Show ensemble by default
    initial["anomalies"] = true; // Show anomalies by default
    return initial;
  });

  // Define consistent colors for specific models
  const modelColorMap: { [key: string]: string } = {
    ARIMA: "rgb(59, 130, 246)", // Blue
    Prophet: "rgb(16, 185, 129)", // Green
    FB_Prophet: "rgb(245, 101, 101)", // Red
    RNN: "rgb(139, 92, 246)", // Purple
    "N-BEATS": "rgb(245, 158, 11)", // Yellow/Amber
    TFT: "rgb(236, 72, 153)", // Pink
    DeepAR: "rgb(14, 165, 233)", // Sky
    ensemble: "rgb(0, 0, 0)", // Black
    anomalies: "rgb(239, 68, 68)", // Red for anomalies
  };

  // Fallback colors for any unknown models
  const fallbackColors = [
    "rgb(34, 197, 94)", // Emerald
    "rgb(168, 85, 247)", // Violet
    "rgb(251, 146, 60)", // Orange
  ];

  // Calculate confidence scores from MAPE values
  const calculateConfidenceScore = (mape: number): number => {
    // Convert MAPE to confidence score (0-1 scale)
    // Lower MAPE = higher confidence
    // Use exponential decay: confidence = e^(-mape/100)
    // Cap at reasonable values: MAPE > 500% = very low confidence
    const normalizedMape = Math.min(mape, 500); // Cap at 500%
    const confidence = Math.exp(-normalizedMape / 100);
    return Math.max(0, Math.min(1, confidence)); // Ensure 0-1 range
  };

  // Get confidence level description
  const getConfidenceLevel = (score: number): string => {
    if (score >= 0.7) return "High";
    if (score >= 0.4) return "Medium";
    if (score >= 0.2) return "Low";
    return "Very Low";
  };

  // Get confidence color
  const getConfidenceColor = (score: number): string => {
    if (score >= 0.7) return "text-green-600";
    if (score >= 0.4) return "text-yellow-600";
    if (score >= 0.2) return "text-orange-600";
    return "text-red-600";
  };

  // Update visible models when forecasts change
  React.useEffect(() => {
    setVisibleModels((prev) => {
      const updated = { ...prev };
      Object.keys(forecasts).forEach((model) => {
        if (!(model in updated)) {
          updated[model] = true;
        }
      });
      return updated;
    });
  }, [forecasts]);

  // Prepare chart data with multiple models
  const prepareChartData = () => {
    // Create a comprehensive date-based data structure
    const dateMap = new Map<
      string,
      { date: Date; formattedDate: string; data: Record<string, number | null> }
    >();
    const datasets: any[] = [];

    // Helper function to add data point to dateMap
    const addDataPoint = (
      dateStr: string,
      key: string,
      value: number | null
    ) => {
      const date = new Date(dateStr);
      const formattedDate = date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });

      if (!dateMap.has(dateStr)) {
        dateMap.set(dateStr, {
          date,
          formattedDate,
          data: {},
        });
      }

      const entry = dateMap.get(dateStr)!;
      entry.data[key] = value;
    };

    // Add historical data
    historicalData.forEach((item) => {
      addDataPoint(item.date, "historical", item.actual_sales);
    });

    // Add forecast data for each model
    Object.entries(forecasts).forEach(([modelName, modelData]) => {
      if (!visibleModels[modelName]) return;
      modelData.forecast_data.forEach((item) => {
        addDataPoint(item.date, `forecast_${modelName}`, item.predicted_sales);
      });
    });

    // Add ensemble forecast data
    if (ensembleForecast.length > 0 && visibleModels["ensemble"]) {
      ensembleForecast.forEach((item) => {
        addDataPoint(item.date, "ensemble", item.predicted_sales);
      });
    }

    // Add anomaly data
    if (anomalies.length > 0 && visibleModels["anomalies"]) {
      anomalies.forEach((anomaly) => {
        addDataPoint(anomaly.date, "anomaly", anomaly.value);
      });
    }

    // Sort dates chronologically and build final arrays
    const sortedEntries = Array.from(dateMap.entries()).sort(
      ([a], [b]) => new Date(a).getTime() - new Date(b).getTime()
    );

    const allDates: string[] = [];
    const allFullDates: Date[] = [];
    const historicalSales: (number | null)[] = [];

    // Initialize data arrays for each model
    const modelDataArrays: Record<string, (number | null)[]> = {};
    Object.keys(forecasts).forEach((modelName) => {
      if (visibleModels[modelName]) {
        modelDataArrays[`forecast_${modelName}`] = [];
      }
    });
    const ensembleData: (number | null)[] = [];
    const anomalyData: (number | null)[] = [];

    // Populate arrays in chronological order
    sortedEntries.forEach(([, entry]) => {
      allDates.push(entry.formattedDate);
      allFullDates.push(entry.date);
      historicalSales.push(entry.data["historical"] || null);

      // Add model forecast data
      Object.keys(modelDataArrays).forEach((key) => {
        modelDataArrays[key].push(entry.data[key] || null);
      });

      ensembleData.push(entry.data["ensemble"] || null);
      anomalyData.push(entry.data["anomaly"] || null);
    });

    // Add historical dataset
    if (historicalData.length > 0) {
      datasets.push({
        label: "Historical Sales",
        data: historicalSales,
        borderColor: "rgb(107, 114, 128)",
        backgroundColor: "rgba(107, 114, 128, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 6,
        pointBackgroundColor: "rgb(107, 114, 128)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 1,
      });
    }

    let fallbackColorIndex = 0;

    // Add forecast datasets for each model
    Object.entries(forecasts).forEach(([modelName]) => {
      if (!visibleModels[modelName]) return;

      const modelForecastData = modelDataArrays[`forecast_${modelName}`];
      // Use consistent color mapping or fallback
      const color =
        modelColorMap[modelName] ||
        fallbackColors[fallbackColorIndex % fallbackColors.length];
      if (!modelColorMap[modelName]) {
        fallbackColorIndex++;
      }

      datasets.push({
        label: `${modelName
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase())}`,
        data: modelForecastData,
        borderColor: color,
        backgroundColor: color.replace("rgb", "rgba").replace(")", ", 0.1)"),
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: color,
        pointBorderColor: "#ffffff",
        pointBorderWidth: 1,
      });
    });

    // Add ensemble forecast if available and visible
    if (ensembleForecast.length > 0 && visibleModels["ensemble"]) {
      datasets.push({
        label: "Ensemble (Average)",
        data: ensembleData,
        borderColor: "rgb(0, 0, 0)",
        backgroundColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 8,
        pointBackgroundColor: "rgb(0, 0, 0)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
        borderDash: [5, 5], // Dashed line for ensemble
      });
    }

    // Add anomalies if available and visible
    if (anomalies.length > 0 && visibleModels["anomalies"]) {
      datasets.push({
        label: "Anomalies",
        data: anomalyData,
        borderColor: "rgb(239, 68, 68)",
        backgroundColor: "rgba(239, 68, 68, 0.8)",
        borderWidth: 0,
        fill: false,
        pointRadius: 6,
        pointHoverRadius: 8,
        pointBackgroundColor: "rgb(239, 68, 68)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
        showLine: false, // Only show points, no line
      });
    }

    return { allDates, allFullDates, datasets };
  };

  const { allDates, allFullDates, datasets } = prepareChartData();

  // Prepare chart data
  const chartData: ChartData<"line"> = {
    labels: allDates,
    datasets: datasets,
  };

  // Chart options for interactivity
  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: "index",
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: `Sales Forecast - ${productInfo.title}`,
        font: {
          size: 18,
          weight: "bold",
        },
        color: "#1f2937",
        padding: 20,
      },
      legend: {
        display: true,
        position: "top",
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
          filter: (legendItem) => {
            // Hide the bounds from legend, only show main prediction
            return legendItem.text === "Predicted Sales";
          },
        },
      },
      tooltip: {
        mode: "index",
        intersect: false,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          title: (context) => {
            const index = context[0].dataIndex;

            // Use the full date from our allFullDates array
            if (allFullDates && allFullDates[index]) {
              const date = allFullDates[index];
              return date.toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              });
            }

            return "Date unavailable";
          },
          label: (context) => {
            const label = context.dataset.label || "";
            const value = context.parsed.y;

            if (label === "Predicted Sales") {
              return `${label}: ${value.toFixed(1)} units`;
            } else if (label === "Upper Bound" || label === "Lower Bound") {
              const index = context.dataIndex;
              // Safety check to prevent undefined errors
              if (!forecastData || !forecastData[index]) {
                return `${label}: ${value.toFixed(1)} units`;
              }
              const lowerBound = forecastData[index].lower_bound;
              const upperBound = forecastData[index].upper_bound;
              if (lowerBound !== undefined && upperBound !== undefined) {
                return `Confidence Range: ${lowerBound.toFixed(
                  1
                )} - ${upperBound.toFixed(1)} units`;
              }
            }
            return "";
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Date",
          font: {
            size: 14,
            weight: "bold",
          },
          color: "#374151",
        },
        grid: {
          color: "rgba(156, 163, 175, 0.2)",
        },
        ticks: {
          color: "#6b7280",
          font: {
            size: 11,
          },
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Predicted Sales (Units)",
          font: {
            size: 14,
            weight: "bold",
          },
          color: "#374151",
        },
        beginAtZero: true,
        grid: {
          color: "rgba(156, 163, 175, 0.2)",
        },
        ticks: {
          color: "#6b7280",
          font: {
            size: 11,
          },
          callback: function (value) {
            return Math.round(Number(value));
          },
        },
      },
    },
    elements: {
      point: {
        radius: 4,
        hoverRadius: 8,
      },
      line: {
        borderJoinStyle: "round",
      },
    },
    animation: {
      duration: 1000,
      easing: "easeInOutQuart",
    },
  };

  // Calculate summary statistics using ensemble forecast or first available model
  const getStatsData = () => {
    if (ensembleForecast.length > 0) {
      return ensembleForecast;
    } else if (forecastData.length > 0) {
      return forecastData;
    } else if (Object.keys(forecasts).length > 0) {
      const firstModel = Object.values(forecasts)[0];
      return firstModel.forecast_data;
    }
    return [];
  };

  const statsData = getStatsData();
  const totalPredictedSales = statsData.reduce(
    (sum, item) => sum + item.predicted_sales,
    0
  );
  const avgDailySales =
    statsData.length > 0 ? totalPredictedSales / statsData.length : 0;
  const maxSalesDay =
    statsData.length > 0
      ? statsData.reduce((max, item) =>
          item.predicted_sales > max.predicted_sales ? item : max
        )
      : { predicted_sales: 0, date: new Date().toISOString() };

  // Calculate historical statistics
  const totalHistoricalSales = historicalData.reduce(
    (sum, item) => sum + item.actual_sales,
    0
  );
  const avgHistoricalSales =
    historicalData.length > 0
      ? totalHistoricalSales / historicalData.length
      : 0;

  if (error) {
    const isInsufficientDataError = error.includes(
      "minimum 10 data points required"
    );

    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center text-red-500 mb-4">
          <AlertCircle size={48} />
        </div>
        <h3 className="text-lg font-semibold text-center mb-2">
          Forecast Error
        </h3>
        <p className="text-gray-600 text-center mb-4">{error}</p>

        {isInsufficientDataError && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-blue-900 mb-2">💡 Quick Fix</h4>
            <p className="text-blue-800 text-sm mb-3">
              This error occurs when there isn't enough historical sales data.
              You can populate sales data from your existing orders to enable
              forecasting.
            </p>
            {onPopulateSalesData && (
              <button
                onClick={onPopulateSalesData}
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors mb-2"
              >
                Populate Sales Data from Orders
              </button>
            )}
          </div>
        )}

        {onRefresh && (
          <button
            onClick={onRefresh}
            className="w-full bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors"
          >
            Try Again
          </button>
        )}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        {/* Loading Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Generating Sales Forecast
              </h3>
              <p className="text-sm text-gray-600">
                Training multiple AI models for accurate predictions...
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Forecast Period</div>
            <div className="text-lg font-semibold text-blue-600">
              {forecastPeriod}
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Processing historical data</span>
            <span>Training models</span>
            <span>Generating predictions</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full animate-pulse"
              style={{ width: "75%" }}
            ></div>
          </div>
        </div>

        {/* Model Cards Loading */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Training Forecasting Models
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {["ARIMA", "Prophet", "FB_Prophet", "RNN", "N-BEATS", "TFT"].map(
              (model, index) => (
                <div key={model} className="bg-gray-50 rounded-lg p-3 border">
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        index < 3
                          ? "bg-green-500"
                          : index < 5
                          ? "bg-yellow-500 animate-pulse"
                          : "bg-gray-300"
                      }`}
                    ></div>
                    <span className="text-xs font-medium text-gray-700">
                      {model}
                    </span>
                  </div>
                  <div className="mt-2">
                    <div className="text-xs text-gray-500">
                      {index < 3
                        ? "Complete"
                        : index < 5
                        ? "Training..."
                        : "Waiting..."}
                    </div>
                  </div>
                </div>
              )
            )}
          </div>
        </div>

        {/* Chart Skeleton */}
        <div className="h-96 mb-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-500 font-medium">
              Preparing forecast visualization
            </p>
            <p className="text-sm text-gray-400 mt-1">
              This may take a few moments...
            </p>
          </div>
        </div>

        {/* Statistics Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {[
            { label: "Historical Avg", color: "gray" },
            { label: "Total Forecast", color: "blue" },
            { label: "Daily Average", color: "green" },
            { label: "Peak Day", color: "purple" },
          ].map((stat) => (
            <div
              key={stat.label}
              className={`bg-${stat.color}-50 rounded-lg p-4 border border-${stat.color}-100`}
            >
              <div className="animate-pulse">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="h-3 bg-gray-200 rounded mb-2"></div>
                    <div className="h-6 bg-gray-300 rounded mb-1"></div>
                    <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                  </div>
                  <div className="w-6 h-6 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Loading Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="w-5 h-5 text-blue-600 mt-0.5">💡</div>
            <div>
              <h4 className="font-medium text-blue-900 mb-1">Did you know?</h4>
              <p className="text-blue-800 text-sm">
                We're using 6 different AI models including ARIMA, Prophet, and
                neural networks to provide the most accurate sales forecasts.
                Each model brings unique strengths to capture different patterns
                in your data.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Model Toggle Buttons */}
      {Object.keys(forecasts).length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Forecasting Models
          </h4>
          <div className="flex flex-wrap gap-2">
            {Object.keys(forecasts).map((modelName) => {
              const modelColor = modelColorMap[modelName] || fallbackColors[0];
              const modelData = forecasts[modelName];
              const mape = modelData?.mape || 0;
              const confidenceScore = calculateConfidenceScore(mape);
              const confidenceLevel = getConfidenceLevel(confidenceScore);
              const confidenceColorClass = getConfidenceColor(confidenceScore);

              const rgbMatch = modelColor.match(
                /rgb\((\d+),\s*(\d+),\s*(\d+)\)/
              );
              const colorStyle = rgbMatch
                ? {
                    backgroundColor: visibleModels[modelName]
                      ? `rgba(${rgbMatch[1]}, ${rgbMatch[2]}, ${rgbMatch[3]}, 0.1)`
                      : "rgb(243, 244, 246)",
                    borderColor: visibleModels[modelName]
                      ? modelColor
                      : "rgb(209, 213, 219)",
                    color: visibleModels[modelName]
                      ? modelColor
                      : "rgb(107, 114, 128)",
                  }
                : {};

              return (
                <button
                  key={modelName}
                  onClick={() =>
                    setVisibleModels((prev) => ({
                      ...prev,
                      [modelName]: !prev[modelName],
                    }))
                  }
                  style={colorStyle}
                  className="px-3 py-2 rounded-lg text-xs font-medium transition-colors border hover:shadow-sm"
                  title={`MAPE: ${mape.toFixed(1)}% | Confidence: ${(
                    confidenceScore * 100
                  ).toFixed(0)}%`}
                >
                  <div className="flex flex-col items-center space-y-1">
                    <div className="flex items-center space-x-1">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: modelColor }}
                      ></div>
                      <span>
                        {modelName
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-[10px]">
                      <span className={`font-semibold ${confidenceColorClass}`}>
                        {confidenceLevel}
                      </span>
                      <span className="text-gray-500">
                        {(confidenceScore * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </button>
              );
            })}
            {ensembleForecast.length > 0 && (
              <button
                onClick={() =>
                  setVisibleModels((prev) => ({
                    ...prev,
                    ensemble: !prev.ensemble,
                  }))
                }
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  visibleModels["ensemble"]
                    ? "bg-black text-white border border-black"
                    : "bg-gray-100 text-gray-600 border border-gray-300"
                }`}
              >
                Ensemble
              </button>
            )}
            {anomalies.length > 0 && (
              <button
                onClick={() =>
                  setVisibleModels((prev) => ({
                    ...prev,
                    anomalies: !prev.anomalies,
                  }))
                }
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  visibleModels["anomalies"]
                    ? "bg-red-100 text-red-800 border border-red-300"
                    : "bg-gray-100 text-gray-600 border border-gray-300"
                }`}
              >
                Anomalies ({anomalies.length})
              </button>
            )}
          </div>
        </div>
      )}

      {/* Interactive Chart - Increased height */}
      <div className="h-96 mb-6">
        <Line ref={chartRef} data={chartData} options={options} />
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        {historicalData.length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">
                  Historical Avg
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {avgHistoricalSales.toFixed(1)} units
                </p>
                <p className="text-xs text-gray-500">
                  Past {historicalData.length} days
                </p>
              </div>
              <BarChart3 className="text-gray-500" size={24} />
            </div>
          </div>
        )}

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 font-medium">
                Total Forecast ({forecastPeriod})
              </p>
              <p className="text-2xl font-bold text-blue-900">
                {totalPredictedSales.toFixed(0)} units
              </p>
            </div>
            <TrendingUp className="text-blue-500" size={24} />
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 font-medium">
                Daily Average
              </p>
              <p className="text-2xl font-bold text-green-900">
                {avgDailySales.toFixed(1)} units
              </p>
            </div>
            <Target className="text-green-500" size={24} />
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-purple-600 font-medium">Peak Day</p>
              <p className="text-lg font-bold text-purple-900">
                {maxSalesDay.predicted_sales.toFixed(0)} units
              </p>
              <p className="text-xs text-purple-600">
                {new Date(maxSalesDay.date).toLocaleDateString()}
              </p>
            </div>
            <Calendar className="text-purple-500" size={24} />
          </div>
        </div>
      </div>

      {/* Product Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold text-gray-900 mb-2">
          Product Information
        </h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">SKU:</span>
            <span className="ml-2 font-medium">{productInfo.sku || "N/A"}</span>
          </div>
          <div>
            <span className="text-gray-600">Current Inventory:</span>
            <span className="ml-2 font-medium">
              {productInfo.current_inventory} units
            </span>
          </div>
        </div>
      </div>

      {/* Refresh Button */}
      {onRefresh && (
        <div className="mt-4 text-center">
          <button
            onClick={onRefresh}
            className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-sm"
          >
            Refresh Forecast
          </button>
        </div>
      )}
    </div>
  );
};

export default SalesForecastChart;
