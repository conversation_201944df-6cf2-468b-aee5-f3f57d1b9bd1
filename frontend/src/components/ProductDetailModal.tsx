import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  X,
  Package,
  Tag,
  Weight,
  Barcode,
  ExternalLink,
  TrendingUp,
  User,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import { fetchProductOrders, type Order } from "../services/orderService";

interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  description_html?: string;
  handle?: string;
  price: number;
  compare_at_price?: number;
  inventory_quantity: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  weight_unit?: string;
  status: string;
  product_type?: string;
  vendor?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  store_id: number;
}

interface ProductDetailModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onViewForecast?: (product: Product) => void;
}

const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  product,
  isOpen,
  onClose,
  onViewForecast,
}) => {
  const navigate = useNavigate();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [imageError, setImageError] = useState<{ [key: number]: boolean }>({});
  const [orders, setOrders] = useState<Order[]>([]);
  const [ordersLoading, setOrdersLoading] = useState(false);

  useEffect(() => {
    if (isOpen && product.id) {
      setOrdersLoading(true);
      fetchProductOrders(product.id)
        .then(setOrders)
        .catch(() => setOrders([]))
        .finally(() => setOrdersLoading(false));
    }
  }, [isOpen, product.id]);

  if (!isOpen) return null;

  const handleImageError = (index: number) => {
    setImageError((prev) => ({ ...prev, [index]: true }));
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      case "archived":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const hasDiscount =
    product.compare_at_price && product.compare_at_price > product.price;
  const discountPercentage = hasDiscount
    ? Math.round(
        ((product.compare_at_price! - product.price) /
          product.compare_at_price!) *
          100
      )
    : 0;

  const handleForecastClick = () => {
    if (onViewForecast) {
      onViewForecast(product);
    }
    onClose();
  };

  const handleOrderClick = (orderId: number) => {
    navigate(`/orders/${orderId}`);
    onClose();
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const getOrderStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "fulfilled":
      case "paid":
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
      case "processing":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
      case "refunded":
        return "bg-red-100 text-red-800 border-red-200";
      case "partially_fulfilled":
      case "partially_paid":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white/90 rounded-2xl max-w-[80vw] w-full max-h-[95vh] overflow-y-auto shadow-2xl border border-blue-100">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-3xl font-bold text-gray-900">{product.title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={28} />
          </button>
        </div>

        <div className="p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
            {/* Images Section */}
            <div>
              {/* Main Image */}
              <div className="mb-4">
                {product.images &&
                product.images.length > 0 &&
                !imageError[selectedImageIndex] ? (
                  <img
                    src={product.images[selectedImageIndex]}
                    alt={product.title}
                    className="w-full h-80 object-cover rounded-lg"
                    onError={() => handleImageError(selectedImageIndex)}
                  />
                ) : (
                  <div className="w-full h-80 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Package className="text-gray-400" size={64} />
                  </div>
                )}
              </div>

              {/* Image Thumbnails */}
              {product.images && product.images.length > 1 && (
                <div className="flex space-x-2 overflow-x-auto">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${
                        selectedImageIndex === index
                          ? "border-blue-500"
                          : "border-gray-200"
                      }`}
                    >
                      {!imageError[index] ? (
                        <img
                          src={image}
                          alt={`${product.title} ${index + 1}`}
                          className="w-full h-full object-cover"
                          onError={() => handleImageError(index)}
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                          <Package className="text-gray-400" size={16} />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Details */}
            <div>
              {/* Status and Vendor */}
              <div className="flex items-center justify-between mb-4">
                <span
                  className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                    product.status
                  )}`}
                >
                  {product.status.charAt(0).toUpperCase() +
                    product.status.slice(1)}
                </span>
                {product.vendor && (
                  <span className="text-sm text-gray-600">
                    by {product.vendor}
                  </span>
                )}
              </div>

              {/* Price */}
              <div className="mb-6">
                <div className="flex items-center space-x-3">
                  <span className="text-3xl font-bold text-gray-900">
                    ${product.price.toFixed(2)}
                  </span>
                  {hasDiscount && (
                    <>
                      <span className="text-lg text-gray-500 line-through">
                        ${product.compare_at_price!.toFixed(2)}
                      </span>
                      <span className="bg-red-500 text-white px-2 py-1 rounded-full text-sm font-bold">
                        -{discountPercentage}% OFF
                      </span>
                    </>
                  )}
                </div>
              </div>

              {/* Description */}
              {product.description && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2">Description</h3>
                  <p className="text-gray-600 leading-relaxed">
                    {product.description}
                  </p>
                </div>
              )}

              {/* Product Details Grid */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                {product.sku && (
                  <div className="flex items-center space-x-2">
                    <Tag className="text-gray-400" size={16} />
                    <div>
                      <p className="text-sm text-gray-500">SKU</p>
                      <p className="font-medium">{product.sku}</p>
                    </div>
                  </div>
                )}

                {product.barcode && (
                  <div className="flex items-center space-x-2">
                    <Barcode className="text-gray-400" size={16} />
                    <div>
                      <p className="text-sm text-gray-500">Barcode</p>
                      <p className="font-medium">{product.barcode}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Package className="text-gray-400" size={16} />
                  <div>
                    <p className="text-sm text-gray-500">Inventory</p>
                    <p className="font-medium">
                      {product.inventory_quantity} units
                    </p>
                  </div>
                </div>

                {product.weight && (
                  <div className="flex items-center space-x-2">
                    <Weight className="text-gray-400" size={16} />
                    <div>
                      <p className="text-sm text-gray-500">Weight</p>
                      <p className="font-medium">
                        {product.weight} {product.weight_unit || "kg"}
                      </p>
                    </div>
                  </div>
                )}

                {product.product_type && (
                  <div className="col-span-2">
                    <p className="text-sm text-gray-500">Product Type</p>
                    <p className="font-medium">{product.product_type}</p>
                  </div>
                )}
              </div>

              {/* Tags */}
              {product.tags && product.tags.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {product.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Variants */}
              {product.variants && product.variants.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2">Variants</h3>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {product.variants.map((variant, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{variant.title}</span>
                          <span className="text-sm text-gray-600">
                            ${variant.price}
                          </span>
                        </div>
                        {variant.sku && (
                          <p className="text-sm text-gray-500">
                            SKU: {variant.sku}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleForecastClick}
                  className="flex-1 bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors font-medium flex items-center justify-center space-x-2"
                >
                  <TrendingUp size={18} />
                  <span>View Sales Forecast</span>
                </button>

                {product.handle && (
                  <button
                    onClick={() =>
                      window.open(
                        `https://shopify.com/admin/products/${product.external_id}`,
                        "_blank"
                      )
                    }
                    className="bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium flex items-center justify-center space-x-2"
                  >
                    <ExternalLink size={18} />
                    <span>Shopify</span>
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Orders Section */}
          <div className="mt-10">
            <h3 className="text-2xl font-semibold mb-4 flex items-center gap-2">
              <DollarSign className="text-blue-500" size={22} /> Orders for this
              Product
            </h3>
            {ordersLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : orders.length > 0 ? (
              <div className="overflow-x-auto rounded-lg shadow">
                <table className="min-w-full bg-white/80">
                  <thead>
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-semibold text-gray-500 uppercase">
                        Order #
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-semibold text-gray-500 uppercase">
                        Customer
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-semibold text-gray-500 uppercase">
                        Date
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-semibold text-gray-500 uppercase">
                        Status
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-semibold text-gray-500 uppercase">
                        Total
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-semibold text-gray-500 uppercase">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr
                        key={order.id}
                        className="hover:bg-blue-50 cursor-pointer transition-colors"
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <td className="px-4 py-2">
                          <div className="font-mono text-blue-700 hover:text-blue-900">
                            {order.order_number || `#${order.external_id}`}
                          </div>
                          <div className="text-xs text-gray-500">
                            ID: {order.external_id}
                          </div>
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex items-center gap-2">
                            <User className="text-gray-400" size={16} />
                            <div>
                              <div className="text-sm text-gray-900">
                                {order.customer_email || "N/A"}
                              </div>
                              {order.customer_id && (
                                <div className="text-xs text-gray-500">
                                  ID: {order.customer_id}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex items-center gap-2">
                            <Calendar className="text-gray-400" size={16} />
                            <div>
                              <div className="text-sm text-gray-900">
                                {new Date(order.order_date).toLocaleDateString(
                                  "en-US",
                                  {
                                    year: "numeric",
                                    month: "short",
                                    day: "numeric",
                                  }
                                )}
                              </div>
                              <div className="text-xs text-gray-500">
                                {new Date(order.order_date).toLocaleTimeString(
                                  "en-US",
                                  {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  }
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-2">
                          <div className="space-y-1">
                            <span
                              className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full border ${getOrderStatusColor(
                                order.status
                              )}`}
                            >
                              {order.status === "completed" ||
                              order.status === "fulfilled" ? (
                                <CheckCircle size={12} />
                              ) : order.status === "pending" ||
                                order.status === "processing" ? (
                                <Clock size={12} />
                              ) : (
                                <XCircle size={12} />
                              )}
                              {order.status}
                            </span>
                            {order.financial_status && (
                              <div className="text-xs text-gray-500">
                                Payment: {order.financial_status}
                              </div>
                            )}
                            {order.fulfillment_status && (
                              <div className="text-xs text-gray-500">
                                Fulfillment: {order.fulfillment_status}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-2">
                          <div className="font-semibold text-gray-900">
                            {formatCurrency(order.total_price, order.currency)}
                          </div>
                          {order.subtotal_price && order.total_tax && (
                            <div className="text-xs text-gray-500">
                              Subtotal:{" "}
                              {formatCurrency(
                                order.subtotal_price,
                                order.currency
                              )}
                              {order.total_tax > 0 && (
                                <span>
                                  {" "}
                                  + Tax:{" "}
                                  {formatCurrency(
                                    order.total_tax,
                                    order.currency
                                  )}
                                </span>
                              )}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-2 text-right">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOrderClick(order.id);
                            }}
                            className="text-blue-600 hover:text-blue-900 flex items-center gap-1 text-sm"
                          >
                            View Details
                            <ExternalLink size={12} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-gray-500 py-8 text-center">
                No orders found for this product.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailModal;
