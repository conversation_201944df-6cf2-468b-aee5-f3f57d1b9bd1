#!/usr/bin/env python3
"""
Debug actual sales data to understand why predictions are so high
"""

import requests
import json

def debug_actual_sales():
    """Debug the actual sales data for product 1"""
    
    print("🔍 Debugging actual sales data for product 1...")
    
    try:
        # Get the debug forecast which includes historical data
        response = requests.get("http://localhost:8000/api/products/debug/1/forecast?forecast_period=1M")
        if response.status_code != 200:
            print(f"❌ Failed to get debug data: {response.status_code}")
            return

        data = response.json()
        
        # Check if we have historical data in the debug info
        debug_info = data.get("debug_info", {})
        print(f"📊 Historical data points: {debug_info.get('sales_data_points', 'N/A')}")
        
        # Let's try to get sales data from a different endpoint
        print("\n🔍 Trying to get recent sales data...")
        
        # Try the orders endpoint to see recent activity
        orders_response = requests.get("http://localhost:8000/api/orders?limit=20")
        if orders_response.status_code == 200:
            orders_data = orders_response.json()
            print(f"📦 Recent orders found: {len(orders_data)}")
            
            # Filter for product 1 and show recent quantities
            product_1_orders = [order for order in orders_data if order.get('product_id') == 1]
            print(f"📦 Product 1 orders in recent data: {len(product_1_orders)}")
            
            if product_1_orders:
                print("\n📊 Recent Product 1 order quantities:")
                for order in product_1_orders[-10:]:  # Last 10 orders
                    print(f"  Date: {order.get('order_date', 'N/A')}, Quantity: {order.get('quantity', 'N/A')}")
        
        # Let's also check what the models are predicting vs recent trends
        print(f"\n📈 Current Predictions:")
        forecasts = data.get("forecasts", {})
        
        if "Prophet" in forecasts:
            prophet_first = forecasts["Prophet"]["forecast_data"][0]["predicted_sales"]
            print(f"  Darts Prophet first prediction: {prophet_first:.2f}")
        
        if "FB_Prophet" in forecasts:
            fb_prophet_first = forecasts["FB_Prophet"]["forecast_data"][0]["predicted_sales"]
            print(f"  FB_Prophet first prediction: {fb_prophet_first:.2f}")
            
        # Show other models for comparison
        for model_name in ["ARIMA", "RNN", "N-BEATS"]:
            if model_name in forecasts:
                first_pred = forecasts[model_name]["forecast_data"][0]["predicted_sales"]
                print(f"  {model_name} first prediction: {first_pred:.2f}")
        
        print(f"\n❓ Question: Are these predictions reasonable based on recent sales?")
        print(f"   If recent sales are much lower (e.g., 0-2 per day), then predictions of 5-7 are too high")
        print(f"   If recent sales are around 5-7 per day, then predictions are reasonable")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_actual_sales()
