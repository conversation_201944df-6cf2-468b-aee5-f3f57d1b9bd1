#!/usr/bin/env python3
"""
Debug FB_Prophet predictions to understand why they're so high
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# import pandas as pd
# import numpy as np
from datetime import datetime, timedelta
import requests
import json


def debug_fb_prophet_data():
    """Debug FB_Prophet data preprocessing"""
    print("🔍 Debugging FB_Prophet data preprocessing...")

    try:
        # Get forecast data for product 1
        response = requests.get("http://localhost:8000/api/products/debug/1/forecast?forecast_period=1M")
        if response.status_code != 200:
            print(f"❌ Failed to get forecast data: {response.status_code}")
            return

        data = response.json()

        # Check if we have FB_Prophet data
        if "FB_Prophet" not in data.get("forecasts", {}):
            print("❌ No FB_Prophet forecast found")
            return

        fb_prophet_data = data["forecasts"]["FB_Prophet"]
        prophet_data = data["forecasts"].get("Prophet", {})

        print(f"📊 FB_Prophet MAPE: {fb_prophet_data.get('mape', 'N/A'):.2f}%")
        print(f"📊 Prophet MAPE: {prophet_data.get('mape', 'N/A'):.2f}%")

        # Compare first few predictions
        fb_predictions = fb_prophet_data.get("forecast_data", [])[:5]
        prophet_predictions = prophet_data.get("forecast_data", [])[:5]

        print("\n🔍 First 5 predictions comparison:")
        print("Date\t\tFB_Prophet\tProphet\t\tDifference")
        print("-" * 60)

        for i in range(min(len(fb_predictions), len(prophet_predictions))):
            fb_pred = fb_predictions[i]["predicted_sales"]
            prophet_pred = prophet_predictions[i]["predicted_sales"]
            diff = fb_pred - prophet_pred
            date = fb_predictions[i]["date"]

            print(f"{date}\t{fb_pred:.2f}\t\t{prophet_pred:.2f}\t\t{diff:+.2f}")

        # Check historical data points
        print(f"\n📈 Historical data points: {data.get('historical_data_points', 'N/A')}")

        # Get all model predictions for comparison
        print("\n📊 All model MAPE scores:")
        for model_name, model_data in data.get("forecasts", {}).items():
            mape = model_data.get("mape", "N/A")
            if isinstance(mape, (int, float)):
                print(f"  {model_name}: {mape:.2f}%")
            else:
                print(f"  {model_name}: {mape}")

        # Check if predictions are reasonable
        fb_avg = sum([p["predicted_sales"] for p in fb_predictions]) / len(fb_predictions) if fb_predictions else 0
        prophet_avg = (
            sum([p["predicted_sales"] for p in prophet_predictions]) / len(prophet_predictions)
            if prophet_predictions
            else 0
        )

        print(f"\n📊 Average predictions:")
        print(f"  FB_Prophet: {fb_avg:.2f}")
        print(f"  Prophet: {prophet_avg:.2f}")
        print(f"  Ratio: {fb_avg/prophet_avg:.2f}x")

        if fb_avg > prophet_avg * 2:
            print("⚠️  WARNING: FB_Prophet predictions are significantly higher than Prophet!")
            print("   This suggests a data preprocessing issue.")

        # Now let's debug the actual sales data
        print("\n🔍 Debugging actual sales data...")
        sales_response = requests.get("http://localhost:8000/api/products/1/sales-data")
        if sales_response.status_code == 200:
            sales_data = sales_response.json()
            if sales_data.get("success") and sales_data.get("data"):
                sales_records = sales_data["data"]
                quantities = [record["quantity_sold"] for record in sales_records]

                print(f"📊 Historical sales statistics:")
                print(f"  Total records: {len(quantities)}")
                print(f"  Min: {min(quantities):.2f}")
                print(f"  Max: {max(quantities):.2f}")
                print(f"  Average: {sum(quantities)/len(quantities):.2f}")
                print(f"  Non-zero records: {sum(1 for q in quantities if q > 0)}")

                # Show first 10 and last 10 values
                print(f"  First 10 values: {quantities[:10]}")
                print(f"  Last 10 values: {quantities[-10:]}")

                # This should help us understand if the issue is in the raw data
                print(f"\n💡 Analysis:")
                avg_sales = sum(quantities) / len(quantities)
                print(f"  Historical average: {avg_sales:.2f}")
                print(f"  FB_Prophet predicting: {fb_avg:.2f} ({fb_avg/avg_sales:.1f}x historical)")
                print(f"  Prophet predicting: {prophet_avg:.2f} ({prophet_avg/avg_sales:.1f}x historical)")

    except Exception as e:
        print(f"❌ Error debugging FB_Prophet: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    debug_fb_prophet_data()
