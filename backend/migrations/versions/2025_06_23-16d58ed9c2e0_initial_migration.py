"""Initial migration

Revision ID: 16d58ed9c2e0
Revises: 
Create Date: 2025-06-23 17:37:03.837262

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '16d58ed9c2e0'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('hashed_password', sa.String(), nullable=False),
    sa.Column('full_name', sa.String(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_table('stores',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('platform', sa.String(), nullable=False),
    sa.Column('admin_access_token', sa.String(), nullable=False),
    sa.Column('storefront_access_token', sa.String(), nullable=True),
    sa.Column('shop_domain', sa.String(), nullable=True),
    sa.Column('shop_id', sa.String(), nullable=True),
    sa.Column('shop_name', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('last_sync', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stores_id'), 'stores', ['id'], unique=False)
    op.create_table('orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('order_number', sa.String(), nullable=True),
    sa.Column('customer_email', sa.String(), nullable=True),
    sa.Column('customer_id', sa.String(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=True),
    sa.Column('subtotal_price', sa.Float(), nullable=True),
    sa.Column('total_tax', sa.Float(), nullable=True),
    sa.Column('total_discounts', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('financial_status', sa.String(), nullable=True),
    sa.Column('fulfillment_status', sa.String(), nullable=True),
    sa.Column('order_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('line_items', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_orders_id'), 'orders', ['id'], unique=False)
    op.create_table('products',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('description_html', sa.Text(), nullable=True),
    sa.Column('handle', sa.String(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('compare_at_price', sa.Float(), nullable=True),
    sa.Column('inventory_quantity', sa.Integer(), nullable=True),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('barcode', sa.String(), nullable=True),
    sa.Column('weight', sa.Float(), nullable=True),
    sa.Column('weight_unit', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('product_type', sa.String(), nullable=True),
    sa.Column('vendor', sa.String(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('images', sa.Text(), nullable=True),
    sa.Column('variants', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_products_id'), 'products', ['id'], unique=False)
    op.create_table('forecast_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('forecast_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('predicted_sales', sa.Float(), nullable=True),
    sa.Column('lower_bound', sa.Float(), nullable=True),
    sa.Column('upper_bound', sa.Float(), nullable=True),
    sa.Column('confidence_interval', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_forecast_data_id'), 'forecast_data', ['id'], unique=False)
    op.create_table('sales_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('quantity_sold', sa.Integer(), nullable=True),
    sa.Column('revenue', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sales_data_id'), 'sales_data', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sales_data_id'), table_name='sales_data')
    op.drop_table('sales_data')
    op.drop_index(op.f('ix_forecast_data_id'), table_name='forecast_data')
    op.drop_table('forecast_data')
    op.drop_index(op.f('ix_products_id'), table_name='products')
    op.drop_table('products')
    op.drop_index(op.f('ix_orders_id'), table_name='orders')
    op.drop_table('orders')
    op.drop_index(op.f('ix_stores_id'), table_name='stores')
    op.drop_table('stores')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
