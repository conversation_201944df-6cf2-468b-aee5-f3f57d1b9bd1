# WooCommerce Integration Setup Guide

This guide will walk you through setting up WooCommerce integration with the E-commerce Hub.

## Prerequisites

- WordPress website with WooCommerce plugin
- Admin access to WordPress/WooCommerce
- SSL certificate (HTTPS) recommended

## Step 1: Set Up WooCommerce Store

### Option A: Local Development Setup

1. **Install Local WordPress**
   ```bash
   # Using Docker
   docker run -d --name wordpress \
     -p 8080:80 \
     -e WORDPRESS_DB_HOST=db \
     -e WORDPRESS_DB_USER=wordpress \
     -e WORDPRESS_DB_PASSWORD=wordpress \
     -e WORDPRESS_DB_NAME=wordpress \
     wordpress:latest
   
   # Or use Local by Flywheel, XAMPP, or similar
   ```

2. **Install WooCommerce Plugin**
   - Access WordPress admin: `http://localhost:8080/wp-admin`
   - Go to Plugins > Add New
   - Search for "WooCommerce"
   - Install and activate the plugin

### Option B: Hosted WordPress

1. **Choose a Hosting Provider**
   - WordPress.com (Business plan or higher)
   - SiteGround, Bluehost, or similar
   - Ensure WooCommerce is supported

2. **Install WooCommerce**
   - From WordPress admin, go to Plugins > Add New
   - Search and install WooCommerce
   - Follow the setup wizard

## Step 2: Configure WooCommerce

1. **Run Setup Wizard**
   - After activation, WooCommerce will prompt you to run the setup wizard
   - Configure:
     - Store location and currency
     - Payment methods (PayPal, Stripe, etc.)
     - Shipping options
     - Tax settings

2. **Add Sample Products**
   - Go to Products > Add New
   - Create test products with:
     - Product name and description
     - Regular price
     - Stock quantity
     - SKU
     - Product images
     - Categories and tags

3. **Configure Store Settings**
   - Go to WooCommerce > Settings
   - Configure:
     - General settings (currency, location)
     - Products settings (inventory, downloadable products)
     - Shipping zones and methods
     - Payment gateways

## Step 3: Generate API Keys

1. **Enable REST API**
   - Go to WooCommerce > Settings > Advanced
   - Click on the "REST API" tab
   - The REST API should be enabled by default

2. **Create API Key**
   - Click "Add key"
   - Fill in the details:
     - **Description**: `E-commerce Hub Integration`
     - **User**: Select an admin user
     - **Permissions**: Select "Read/Write"
   - Click "Generate API key"

3. **Save Credentials**
   - Copy the generated credentials:
     - **Consumer Key**: `ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
     - **Consumer Secret**: `cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
   - Store these securely - they won't be shown again

## Step 4: Test API Access

### Using cURL (Optional Testing)

```bash
# Test API connection
curl -u "consumer_key:consumer_secret" \
  https://your-store.com/wp-json/wc/v3/products

# Example response should show products list
```

### Using Postman (Optional Testing)

1. **Create New Request**
   - Method: GET
   - URL: `https://your-store.com/wp-json/wc/v3/products`

2. **Add Authentication**
   - Type: Basic Auth
   - Username: Your consumer key
   - Password: Your consumer secret

## Step 5: Connect to E-commerce Hub

1. **Access E-commerce Hub**
   - Login to your deployed application
   - Navigate to the Stores page

2. **Add WooCommerce Store**
   - Click "Add Store"
   - Fill in the connection form:
     - **Store Name**: Your store name
     - **Platform**: Select "WooCommerce"
     - **Store URL**: `https://your-store.com`
     - **API Key**: Consumer Key from Step 3
     - **API Secret**: Consumer Secret from Step 3

3. **Test Connection**
   - Click "Test Connection"
   - Verify successful connection
   - Review store information displayed

4. **Save and Sync**
   - Click "Add Store" to save
   - Click "Sync" to import products and orders

## Step 6: Verify Data Sync

1. **Check Products**
   - Go to Products page in E-commerce Hub
   - Verify all products are imported correctly
   - Check prices, inventory, and SKUs

2. **Check Orders**
   - Go to Orders page
   - Verify order history is imported
   - Check order statuses and totals

## Troubleshooting

### Common Issues:

1. **"Consumer key is invalid" Error**
   - Verify the consumer key is copied correctly
   - Ensure no extra spaces or characters
   - Check that the API key is active

2. **"SSL certificate problem" Error**
   - Ensure your WordPress site has a valid SSL certificate
   - Use HTTPS in the store URL
   - Consider using a staging environment for testing

3. **"REST API is disabled" Error**
   - Go to WooCommerce > Settings > Advanced > REST API
   - Ensure REST API is enabled
   - Check WordPress permalink settings

4. **"Permission denied" Error**
   - Verify API key permissions are set to "Read/Write"
   - Check that the user associated with the key has admin privileges
   - Ensure WooCommerce is properly activated

5. **"404 Not Found" Error**
   - Check the store URL format
   - Verify WooCommerce is installed and active
   - Test the API endpoint directly in browser

### WordPress/WooCommerce Specific:

1. **Permalink Issues**
   - Go to Settings > Permalinks
   - Choose "Post name" or "Custom Structure"
   - Click "Save Changes"

2. **Plugin Conflicts**
   - Deactivate other plugins temporarily
   - Test if the issue persists
   - Reactivate plugins one by one to identify conflicts

3. **Memory Limits**
   - Large stores may hit PHP memory limits
   - Increase memory limit in wp-config.php:
     ```php
     ini_set('memory_limit', '256M');
     ```

## Advanced Configuration

### Webhook Setup (Real-time Updates)

1. **Install Webhook Plugin**
   - Install "WooCommerce Webhooks" plugin
   - Or use built-in webhook functionality

2. **Configure Webhooks**
   - Go to WooCommerce > Settings > Advanced > Webhooks
   - Add webhooks for:
     - Product created/updated/deleted
     - Order created/updated/deleted
     - Customer created/updated

3. **Webhook Endpoints**
   - Product webhook: `https://your-api-url/webhooks/woocommerce/products`
   - Order webhook: `https://your-api-url/webhooks/woocommerce/orders`

### Custom Fields and Metadata

If you use custom fields:

1. **Product Meta Fields**
   - Custom attributes will be synced automatically
   - Use WooCommerce product meta fields

2. **Order Meta Fields**
   - Custom order fields are supported
   - Ensure proper field naming conventions

## Security Best Practices

1. **API Key Security**
   - Never share API keys publicly
   - Use environment variables for credentials
   - Regularly rotate API keys

2. **WordPress Security**
   - Keep WordPress and plugins updated
   - Use strong admin passwords
   - Install security plugins (Wordfence, etc.)
   - Enable two-factor authentication

3. **SSL/HTTPS**
   - Always use HTTPS for production
   - Ensure valid SSL certificates
   - Test SSL configuration

## Performance Optimization

1. **Caching**
   - Use caching plugins (WP Rocket, W3 Total Cache)
   - Configure object caching
   - Enable CDN if needed

2. **Database Optimization**
   - Regular database cleanup
   - Optimize database tables
   - Monitor database performance

3. **API Rate Limiting**
   - WooCommerce has built-in rate limiting
   - Monitor API usage
   - Implement proper error handling

## Support Resources

- **WooCommerce Documentation**: https://docs.woocommerce.com/
- **REST API Documentation**: https://woocommerce.github.io/woocommerce-rest-api-docs/
- **WordPress Support**: https://wordpress.org/support/
- **Community Forums**: https://wordpress.org/support/plugin/woocommerce/
