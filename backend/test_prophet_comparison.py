#!/usr/bin/env python3
"""
Direct comparison test between <PERSON><PERSON> Prophet and Facebook Prophet
to verify they produce identical results with identical data and parameters
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_prophet_comparison():
    """Test that both Prophet implementations produce identical results"""
    
    # Create identical test data
    dates = pd.date_range(start='2024-01-01', end='2024-06-01', freq='D')
    
    # Create sparse sales data similar to real e-commerce data
    np.random.seed(42)  # For reproducible results
    sales = []
    for i, date in enumerate(dates):
        # Simulate sparse sales with some weekly pattern
        base_sales = 2 + 3 * np.sin(2 * np.pi * i / 7)  # Weekly pattern
        if np.random.random() < 0.7:  # 70% chance of sales
            daily_sales = max(0.1, base_sales + np.random.normal(0, 1))
        else:
            daily_sales = 0.1  # Minimum value for Prophet
        sales.append(daily_sales)
    
    # Create DataFrame
    df = pd.DataFrame({
        'date': dates,
        'quantity_sold': sales
    })
    
    print(f"📊 Test data created: {len(df)} days")
    print(f"   Sales range: {min(sales):.2f} - {max(sales):.2f}")
    print(f"   Average: {np.mean(sales):.2f}")
    print(f"   Non-zero days: {sum(1 for s in sales if s > 0.1)}/{len(sales)}")
    
    # Test 1: Direct Facebook Prophet
    print("\n🔍 Testing Direct Facebook Prophet...")
    try:
        from prophet import Prophet as FBProphet
        
        # Prepare data for FB Prophet
        fb_df = df.copy()
        fb_df['ds'] = fb_df['date']
        fb_df['y'] = fb_df['quantity_sold']
        fb_df = fb_df[['ds', 'y']]
        
        # Create FB Prophet model with exact parameters
        fb_model = FBProphet(
            daily_seasonality=False,
            weekly_seasonality=True,
            yearly_seasonality=False,
            seasonality_mode="additive",
            changepoint_prior_scale=0.1,
            seasonality_prior_scale=1.0,
        )
        
        # Fit and predict
        fb_model.fit(fb_df)
        fb_future = fb_model.make_future_dataframe(periods=7)
        fb_forecast = fb_model.predict(fb_future)
        
        # Get future predictions
        fb_predictions = fb_forecast.tail(7)['yhat'].tolist()
        print(f"   FB Prophet predictions: {[f'{p:.2f}' for p in fb_predictions]}")
        
    except Exception as e:
        print(f"   ❌ FB Prophet failed: {e}")
        fb_predictions = None
    
    # Test 2: Darts Prophet
    print("\n🔍 Testing Darts Prophet...")
    try:
        from darts import TimeSeries
        from darts.models import Prophet as DartsProphet
        
        # Prepare data for Darts
        darts_df = df.copy()
        darts_ts = TimeSeries.from_dataframe(
            darts_df, 
            time_col='date', 
            value_cols='quantity_sold', 
            freq='D'
        )
        
        # Create Darts Prophet model with exact same parameters
        darts_model = DartsProphet(
            daily_seasonality=False,
            weekly_seasonality=True,
            yearly_seasonality=False,
            seasonality_mode="additive",
            changepoint_prior_scale=0.1,
            seasonality_prior_scale=1.0,
        )
        
        # Fit and predict
        darts_model.fit(darts_ts)
        darts_forecast = darts_model.predict(n=7)
        
        # Get predictions
        darts_predictions = darts_forecast.values().flatten().tolist()
        print(f"   Darts Prophet predictions: {[f'{p:.2f}' for p in darts_predictions]}")
        
    except Exception as e:
        print(f"   ❌ Darts Prophet failed: {e}")
        darts_predictions = None
    
    # Compare results
    if fb_predictions and darts_predictions:
        print("\n📊 Comparison Results:")
        print("Day\tFB_Prophet\tDarts_Prophet\tDifference\tRatio")
        print("-" * 60)
        
        total_fb = 0
        total_darts = 0
        
        for i in range(7):
            fb_pred = fb_predictions[i]
            darts_pred = darts_predictions[i]
            diff = fb_pred - darts_pred
            ratio = fb_pred / darts_pred if darts_pred != 0 else float('inf')
            
            total_fb += fb_pred
            total_darts += darts_pred
            
            print(f"{i+1}\t{fb_pred:.2f}\t\t{darts_pred:.2f}\t\t{diff:+.2f}\t\t{ratio:.2f}x")
        
        avg_fb = total_fb / 7
        avg_darts = total_darts / 7
        avg_ratio = avg_fb / avg_darts if avg_darts != 0 else float('inf')
        
        print(f"\nAverage:\t{avg_fb:.2f}\t\t{avg_darts:.2f}\t\t{avg_fb-avg_darts:+.2f}\t\t{avg_ratio:.2f}x")
        
        if abs(avg_ratio - 1.0) < 0.1:
            print("✅ Results are very similar (within 10%)")
        elif abs(avg_ratio - 1.0) < 0.5:
            print("⚠️  Results are somewhat different (within 50%)")
        else:
            print("❌ Results are significantly different (>50% difference)")
            print("   This suggests different internal configurations or data handling")
    
    else:
        print("❌ Could not compare - one or both models failed")

if __name__ == "__main__":
    test_prophet_comparison()
