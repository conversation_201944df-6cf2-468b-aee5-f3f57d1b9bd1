# Shopify Integration Setup Guide

This guide will walk you through setting up Shopify integration with the E-commerce Hub.

## Prerequisites

- Shopify Partner account
- Development store or existing Shopify store
- Admin access to the store

## Step 1: Create Shopify Partner Account

1. **Sign up for Shopify Partners**
   - Visit https://partners.shopify.com/
   - Click "Join now" and fill out the registration form
   - Verify your email address
   - Complete your partner profile

2. **Create Development Store**
   - In your Partner Dashboard, click "Stores"
   - Click "Add store" > "Development store"
   - Choose "Create a new development store"
   - Fill in store details:
     - Store name: `your-test-store`
     - Password: Create a secure password
     - Store purpose: Select "Test an app or theme"
   - Click "Save"

## Step 2: Create Private App (Recommended Method)

### For Development Stores:

1. **Access Your Development Store**
   - From Partner Dashboard, click on your development store
   - Click "Open store" to access the admin panel

2. **Enable Private App Development**
   - Go to Apps > App and sales channel settings
   - Scroll down to "Private apps"
   - Click "Allow private app development"
   - Click "Allow private app development" again to confirm

3. **Create Private App**
   - Click "Create private app"
   - Fill in app details:
     - App name: `E-commerce Hub Integration`
     - Emergency developer email: Your email

4. **Configure API Permissions**
   - In the "Admin API access scopes" section, enable:
     - **Products**: `read_products`
     - **Orders**: `read_orders`
     - **Inventory**: `read_inventory`
     - **Customers**: `read_customers` (optional)
     - **Analytics**: `read_analytics` (optional)

5. **Generate API Credentials**
   - Click "Save"
   - Copy the following credentials:
     - **API key**: This is your API key
     - **API secret key**: This is your API secret
     - **Admin API access token**: Keep this secure

## Step 3: Alternative - Create Public App (For Production)

### If you need a public app for production use:

1. **Create App in Partner Dashboard**
   - Go to Apps > "Create app"
   - Choose "Public app"
   - Fill in app information

2. **Configure App Settings**
   - Set App URL: `https://your-domain.com`
   - Set Allowed redirection URL(s): `https://your-domain.com/auth/callback`

3. **Set Required Scopes**
   - Add the same scopes as mentioned above
   - Submit for review if required

## Step 4: Test Store Setup

1. **Add Sample Products**
   - Go to Products > Add product
   - Create a few test products with:
     - Title and description
     - Price
     - Inventory quantity
     - SKU
     - Images (optional)

2. **Create Test Orders**
   - Go to Orders > Create order
   - Add products to the order
   - Set customer information
   - Complete the order

## Step 5: Connect to E-commerce Hub

1. **Login to E-commerce Hub**
   - Access your deployed application
   - Login with your account

2. **Add Shopify Store**
   - Go to Stores page
   - Click "Add Store"
   - Fill in the form:
     - **Store Name**: Your store name
     - **Platform**: Select "Shopify"
     - **Store URL**: `your-store.myshopify.com`
     - **API Key**: From Step 2
     - **API Secret**: From Step 2

3. **Test Connection**
   - Click "Test Connection"
   - Verify the connection is successful
   - Click "Add Store" to save

4. **Sync Data**
   - Click "Sync" on your connected store
   - Verify products and orders appear in the dashboard

## Troubleshooting

### Common Issues:

1. **"API key invalid" Error**
   - Verify you're using the correct API key and secret
   - Ensure the private app is enabled
   - Check that required permissions are granted

2. **"Store not found" Error**
   - Verify the store URL format: `store-name.myshopify.com`
   - Ensure the store is active and accessible

3. **"Permission denied" Error**
   - Check that all required API scopes are enabled
   - Verify the API access token is valid

4. **Connection timeout**
   - Check your internet connection
   - Verify Shopify's API status
   - Try again after a few minutes

### API Rate Limits:

- Shopify has API rate limits (40 requests per app per store per minute)
- The app handles rate limiting automatically
- Large stores may take longer to sync

## Best Practices

1. **Security**
   - Never share your API credentials
   - Use environment variables for credentials
   - Regularly rotate API keys

2. **Testing**
   - Always test with development stores first
   - Verify all required data is syncing correctly
   - Test error scenarios

3. **Production**
   - Use webhook notifications for real-time updates
   - Implement proper error handling
   - Monitor API usage and limits

## Webhook Setup (Advanced)

For real-time updates, you can set up webhooks:

1. **In Shopify Admin**
   - Go to Settings > Notifications
   - Scroll to "Webhooks" section
   - Add webhook endpoints for:
     - Product updates
     - Order updates
     - Inventory updates

2. **Webhook URLs**
   - Products: `https://your-api-url/webhooks/shopify/products`
   - Orders: `https://your-api-url/webhooks/shopify/orders`

## Support

If you encounter issues:
1. Check Shopify's API documentation
2. Verify your Partner account status
3. Contact support with error details
4. Check the application logs for detailed error messages
