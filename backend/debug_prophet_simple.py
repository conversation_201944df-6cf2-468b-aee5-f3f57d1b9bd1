#!/usr/bin/env python3
"""
Simple Prophet debugging script - compare basic Prophet with API implementation
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from prophet import Prophet as <PERSON>B<PERSON><PERSON><PERSON>

# Add the backend directory to Python path
sys.path.append('/app')

from database import get_db
from services.forecasting_service import SalesForecastingService

def create_simple_test_data():
    """Create simple test data for debugging"""
    print("📊 Creating simple test data...")
    
    # Create 60 days of simple sales data
    start_date = datetime(2024, 1, 1)
    dates = [start_date + timedelta(days=i) for i in range(60)]
    
    # Simple sales pattern: base 10 + some variation
    np.random.seed(42)  # For reproducible results
    sales = [max(1, 10 + np.random.normal(0, 3) + 2 * np.sin(i * 0.1)) for i in range(60)]
    
    df = pd.DataFrame({
        'date': dates,
        'quantity_sold': sales
    })
    
    print(f"   Created {len(df)} days of data")
    print(f"   Date range: {df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}")
    print(f"   Sales range: {df['quantity_sold'].min():.2f} to {df['quantity_sold'].max():.2f}")
    print(f"   Last 5 days:")
    for _, row in df.tail(5).iterrows():
        print(f"     {row['date'].strftime('%Y-%m-%d')}: {row['quantity_sold']:.2f}")
    
    return df

def test_basic_prophet(data, days_ahead=30):
    """Test Prophet with the most basic implementation possible"""
    print(f"\n🔍 Testing BASIC Prophet (forecasting {days_ahead} days)...")
    
    try:
        # Prepare data in Prophet format (ds, y)
        prophet_df = data.copy()
        prophet_df['ds'] = prophet_df['date']
        prophet_df['y'] = prophet_df['quantity_sold']
        prophet_df = prophet_df[['ds', 'y']]
        
        print(f"   Prophet input data shape: {prophet_df.shape}")
        print(f"   Last historical date: {prophet_df['ds'].max().strftime('%Y-%m-%d')}")
        
        # Create Prophet model with minimal configuration
        model = FBProphet(
            daily_seasonality=False,
            weekly_seasonality=True,
            yearly_seasonality=False,
            seasonality_mode='additive'
        )
        
        # Fit the model
        print("   Fitting Prophet model...")
        model.fit(prophet_df)
        
        # Create future dataframe - BASIC approach
        print("   Creating future dataframe...")
        future = model.make_future_dataframe(periods=days_ahead)
        print(f"   Future dataframe shape: {future.shape}")
        print(f"   Future date range: {future['ds'].min().strftime('%Y-%m-%d')} to {future['ds'].max().strftime('%Y-%m-%d')}")
        
        # Generate forecast
        print("   Generating forecast...")
        forecast = model.predict(future)
        
        # Extract ONLY the future predictions (not historical)
        last_historical_date = prophet_df['ds'].max()
        future_forecast = forecast[forecast['ds'] > last_historical_date].copy()
        
        print(f"   ✅ Basic Prophet SUCCESS!")
        print(f"   Historical data ends: {last_historical_date.strftime('%Y-%m-%d')}")
        print(f"   Forecast starts: {future_forecast['ds'].min().strftime('%Y-%m-%d')}")
        print(f"   Forecast ends: {future_forecast['ds'].max().strftime('%Y-%m-%d')}")
        print(f"   Forecast points: {len(future_forecast)}")
        
        # Show first 5 forecast points
        print(f"   First 5 forecast points:")
        for _, row in future_forecast.head(5).iterrows():
            print(f"     {row['ds'].strftime('%Y-%m-%d')}: {row['yhat']:.2f}")
        
        return {
            'success': True,
            'last_historical_date': last_historical_date,
            'forecast_start_date': future_forecast['ds'].min(),
            'forecast_data': future_forecast[['ds', 'yhat', 'yhat_lower', 'yhat_upper']].to_dict('records')
        }
        
    except Exception as e:
        print(f"   ❌ Basic Prophet FAILED: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_api_prophet_with_same_data(test_data, days_ahead=30):
    """Test Prophet through our API implementation using the SAME test data"""
    print(f"\n🔍 Testing API Prophet with SAME test data (forecasting {days_ahead} days)...")

    try:
        # Initialize database and forecasting service
        db = next(get_db())
        forecasting_service = SalesForecastingService(db)

        # Convert test data to the format expected by API
        sales_data = []
        for _, row in test_data.iterrows():
            sales_data.append({
                'date': row['date'],
                'quantity_sold': row['quantity_sold']
            })

        print(f"   Using {len(sales_data)} test records")
        print(f"   Last 5 test records:")
        for record in sales_data[-5:]:
            print(f"     {record['date']}: {record['quantity_sold']:.2f}")

        # Test Facebook Prophet implementation
        result = forecasting_service._generate_fb_prophet_forecast(sales_data, "FB_Prophet", days_ahead)

        if result:
            print(f"   ✅ API Prophet SUCCESS!")
            print(f"   Forecast points: {len(result['forecast_data'])}")

            # Show first 5 forecast points
            print(f"   First 5 forecast points:")
            for point in result['forecast_data'][:5]:
                print(f"     {point['date']}: {point['predicted_sales']:.2f}")

            return {
                'success': True,
                'forecast_data': result['forecast_data']
            }
        else:
            print(f"   ❌ API Prophet returned None")
            return {'success': False, 'error': 'API returned None'}

    except Exception as e:
        print(f"   ❌ API Prophet FAILED: {str(e)}")
        return {'success': False, 'error': str(e)}

def compare_results(basic_result, api_result):
    """Compare the results from basic and API Prophet"""
    print(f"\n📊 COMPARISON RESULTS:")
    print("=" * 50)
    
    if not basic_result['success']:
        print(f"❌ Basic Prophet failed: {basic_result.get('error', 'Unknown error')}")
        return
    
    if not api_result['success']:
        print(f"❌ API Prophet failed: {api_result.get('error', 'Unknown error')}")
        return
    
    # Compare forecast start dates
    basic_start = basic_result['forecast_start_date']
    api_start_str = api_result['forecast_data'][0]['date']
    api_start = datetime.strptime(api_start_str, '%Y-%m-%d')
    
    print(f"📅 FORECAST START DATES:")
    print(f"   Basic Prophet: {basic_start.strftime('%Y-%m-%d')}")
    print(f"   API Prophet:   {api_start.strftime('%Y-%m-%d')}")
    
    if basic_start.date() == api_start.date():
        print(f"   ✅ START DATES MATCH!")
    else:
        days_diff = (api_start - basic_start).days
        print(f"   ❌ START DATES DIFFER by {days_diff} days")
    
    # Compare first few predictions
    print(f"\n📈 FIRST 5 PREDICTIONS:")
    print(f"   Date        | Basic    | API      | Diff")
    print(f"   ------------|----------|----------|----------")
    
    for i in range(min(5, len(basic_result['forecast_data']), len(api_result['forecast_data']))):
        basic_point = basic_result['forecast_data'][i]
        api_point = api_result['forecast_data'][i]
        
        basic_date = basic_point['ds'].strftime('%Y-%m-%d')
        basic_value = basic_point['yhat']
        api_date = api_point['date']
        api_value = api_point['predicted_sales']
        
        diff = abs(basic_value - api_value)
        
        print(f"   {basic_date} | {basic_value:8.2f} | {api_value:8.2f} | {diff:8.2f}")

def main():
    """Main debugging function"""
    print("🚀 PROPHET DEBUGGING - BASIC vs API COMPARISON")
    print("=" * 60)

    # Test with simple synthetic data first
    test_data = create_simple_test_data()
    days_ahead = 30

    # Test basic Prophet
    basic_result = test_basic_prophet(test_data, days_ahead)

    # Test API Prophet with the SAME test data
    api_result = test_api_prophet_with_same_data(test_data, days_ahead)

    # Compare results
    compare_results(basic_result, api_result)

    print(f"\n🎯 DEBUGGING COMPLETE!")

if __name__ == "__main__":
    main()
