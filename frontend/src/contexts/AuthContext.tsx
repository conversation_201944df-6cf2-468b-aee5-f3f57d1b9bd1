import React, { useState, useEffect } from "react";
import type { ReactNode } from "react";
import { authService } from "../services/authService";
import { AuthContext } from "./AuthContextObject";

interface User {
  id: number;
  email: string;
  full_name: string;
  is_active: boolean;
}

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(() => {
    const storedToken = localStorage.getItem("token");
    const expiry = localStorage.getItem("token_expiry");
    if (storedToken && expiry) {
      if (Date.now() > parseInt(expiry, 10)) {
        localStorage.removeItem("token");
        localStorage.removeItem("token_expiry");
        return null;
      }
      return storedToken;
    }
    return null;
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      if (token) {
        try {
          const userData = await authService.getCurrentUser();
          setUser(userData);
        } catch {
          localStorage.removeItem("token");
          localStorage.removeItem("token_expiry");
          setToken(null);
        }
      }
      setLoading(false);
    };

    initAuth();
  }, [token]);

  const login = async (email: string, password: string) => {
    const response = await authService.login(email, password);
    setToken(response.access_token);
    localStorage.setItem("token", response.access_token);
    // Set expiry for 30 days from now
    const expiry = Date.now() + 30 * 24 * 60 * 60 * 1000;
    localStorage.setItem("token_expiry", expiry.toString());

    const userData = await authService.getCurrentUser();
    setUser(userData);
  };

  const register = async (
    email: string,
    password: string,
    fullName: string
  ) => {
    await authService.register(email, password, fullName);
    await login(email, password);
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem("token");
    localStorage.removeItem("token_expiry");
  };

  const value = {
    user,
    token,
    login,
    register,
    logout,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
