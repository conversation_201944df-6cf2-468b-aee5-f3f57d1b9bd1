#!/usr/bin/env python3
"""
Test individual Prophet models to see if they're getting the corrected data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from services.forecasting_service import SalesForecastingService

def test_individual_prophet():
    """Test individual Prophet models"""
    
    print("🔍 Testing Individual Prophet Models...")
    print("=" * 60)
    
    # Get database session
    db = next(get_db())
    
    try:
        # Initialize forecasting service
        service = SalesForecastingService(db)
        
        # Get raw sales data
        raw_sales_data = service._get_historical_sales_data(1)
        print(f"📊 Raw Sales Data: {len(raw_sales_data)} records")
        
        # Test Facebook Prophet directly
        print(f"\n🔮 Testing Facebook Prophet...")
        try:
            fb_result = service._generate_fb_prophet_forecast("FB_Prophet", raw_sales_data, 7)
            
            if fb_result and fb_result.get("forecast_data"):
                predictions = [p["predicted_sales"] for p in fb_result["forecast_data"]]
                avg_pred = sum(predictions) / len(predictions)
                
                print(f"  ✅ FB Prophet Success:")
                print(f"    First day: {predictions[0]:.2f} units")
                print(f"    Average (7 days): {avg_pred:.2f} units/day")
                print(f"    Range: {min(predictions):.2f} to {max(predictions):.2f}")
                
                # Compare with expected
                expected_avg = 6.70  # From our analysis
                ratio = avg_pred / expected_avg
                print(f"    Ratio vs expected (6.70): {ratio:.2f}")
                
                if ratio > 0.7:
                    print(f"    ✅ GOOD: Predictions are reasonable!")
                elif ratio > 0.4:
                    print(f"    ⚠️  MODERATE: Predictions are somewhat low")
                else:
                    print(f"    ❌ LOW: Predictions are too low")
                    
            else:
                print(f"  ❌ FB Prophet failed or returned no data")
                
        except Exception as e:
            print(f"  ❌ FB Prophet error: {e}")
        
        # Test Darts Prophet
        print(f"\n🔮 Testing Darts Prophet...")
        try:
            # Test if we can generate a single Darts Prophet forecast
            darts_result = service._generate_single_model_forecast("Prophet", raw_sales_data, 7)
            
            if darts_result and darts_result.get("forecast_data"):
                predictions = [p["predicted_sales"] for p in darts_result["forecast_data"]]
                avg_pred = sum(predictions) / len(predictions)
                
                print(f"  ✅ Darts Prophet Success:")
                print(f"    First day: {predictions[0]:.2f} units")
                print(f"    Average (7 days): {avg_pred:.2f} units/day")
                print(f"    Range: {min(predictions):.2f} to {max(predictions):.2f}")
                
                # Compare with expected
                expected_avg = 6.70
                ratio = avg_pred / expected_avg
                print(f"    Ratio vs expected (6.70): {ratio:.2f}")
                
                if ratio > 0.7:
                    print(f"    ✅ GOOD: Predictions are reasonable!")
                elif ratio > 0.4:
                    print(f"    ⚠️  MODERATE: Predictions are somewhat low")
                else:
                    print(f"    ❌ LOW: Predictions are too low")
                    
            else:
                print(f"  ❌ Darts Prophet failed or returned no data")
                
        except Exception as e:
            print(f"  ❌ Darts Prophet error: {e}")
            import traceback
            traceback.print_exc()
        
        # Test the ensemble
        print(f"\n🔮 Testing Ensemble...")
        try:
            ensemble_result = service.generate_multi_model_forecast(1, 7)
            
            if ensemble_result and ensemble_result.get("success"):
                ensemble = ensemble_result.get("ensemble_forecast", {})
                if ensemble and ensemble.get("forecast_data"):
                    predictions = [p["predicted_sales"] for p in ensemble["forecast_data"]]
                    avg_pred = sum(predictions) / len(predictions)
                    
                    print(f"  ✅ Ensemble Success:")
                    print(f"    First day: {predictions[0]:.2f} units")
                    print(f"    Average (7 days): {avg_pred:.2f} units/day")
                    print(f"    Range: {min(predictions):.2f} to {max(predictions):.2f}")
                    
                    # Show individual model results
                    forecasts = ensemble_result.get("forecasts", {})
                    print(f"    Individual models:")
                    for model_name, model_result in forecasts.items():
                        if model_result and model_result.get("forecast_data"):
                            model_preds = [p["predicted_sales"] for p in model_result["forecast_data"]]
                            model_avg = sum(model_preds) / len(model_preds)
                            print(f"      {model_name}: {model_avg:.2f} units/day")
                else:
                    print(f"  ❌ Ensemble returned no forecast data")
            else:
                print(f"  ❌ Ensemble failed: {ensemble_result.get('message', 'Unknown error')}")
                
        except Exception as e:
            print(f"  ❌ Ensemble error: {e}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_individual_prophet()
