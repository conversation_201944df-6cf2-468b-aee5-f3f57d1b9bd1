import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock

from main import app
from tests.test_auth import override_get_db

app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)

@pytest.fixture
def authenticated_user():
    """Create and authenticate a test user"""
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword",
        "full_name": "Test User"
    }
    
    # Register user
    client.post("/api/auth/register", json=user_data)
    
    # Login and get token
    login_response = client.post("/api/auth/login", json={
        "email": user_data["email"],
        "password": user_data["password"]
    })
    token = login_response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def shopify_store_data():
    return {
        "name": "Test Shopify Store",
        "platform": "shopify",
        "store_url": "test-store.myshopify.com",
        "api_key": "test_api_key",
        "api_secret": "test_api_secret"
    }

@pytest.fixture
def woocommerce_store_data():
    return {
        "name": "Test WooCommerce Store",
        "platform": "woocommerce",
        "store_url": "https://test-store.com",
        "api_key": "ck_test_consumer_key",
        "api_secret": "cs_test_consumer_secret"
    }

def test_get_stores_empty(authenticated_user):
    """Test getting stores when user has no stores"""
    response = client.get("/api/stores/", headers=authenticated_user)
    assert response.status_code == 200
    assert response.json() == []

@patch('routers.stores.test_shopify_connection')
def test_create_shopify_store(mock_shopify_test, authenticated_user, shopify_store_data):
    """Test creating a Shopify store"""
    # Mock successful connection test
    mock_shopify_test.return_value = {
        "success": True,
        "message": "Successfully connected to Shopify store",
        "store_info": {"name": "Test Store", "domain": "test-store.myshopify.com"}
    }
    
    response = client.post("/api/stores/", json=shopify_store_data, headers=authenticated_user)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == shopify_store_data["name"]
    assert data["platform"] == "shopify"
    assert data["store_url"] == shopify_store_data["store_url"]

@patch('routers.stores.test_woocommerce_connection')
def test_create_woocommerce_store(mock_woo_test, authenticated_user, woocommerce_store_data):
    """Test creating a WooCommerce store"""
    # Mock successful connection test
    mock_woo_test.return_value = {
        "success": True,
        "message": "Successfully connected to WooCommerce store",
        "store_info": {"name": "Test Store", "url": "https://test-store.com"}
    }
    
    response = client.post("/api/stores/", json=woocommerce_store_data, headers=authenticated_user)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == woocommerce_store_data["name"]
    assert data["platform"] == "woocommerce"

@patch('routers.stores.test_shopify_connection')
def test_create_store_connection_failed(mock_shopify_test, authenticated_user, shopify_store_data):
    """Test creating store with failed connection"""
    # Mock failed connection test
    mock_shopify_test.return_value = {
        "success": False,
        "message": "Invalid API credentials"
    }
    
    response = client.post("/api/stores/", json=shopify_store_data, headers=authenticated_user)
    assert response.status_code == 400
    assert "Failed to connect to store" in response.json()["detail"]

def test_create_store_unauthorized():
    """Test creating store without authentication"""
    store_data = {
        "name": "Test Store",
        "platform": "shopify",
        "store_url": "test-store.myshopify.com",
        "api_key": "test_key",
        "api_secret": "test_secret"
    }
    
    response = client.post("/api/stores/", json=store_data)
    assert response.status_code == 401

@patch('routers.stores.test_shopify_connection')
def test_test_connection_endpoint(mock_shopify_test, authenticated_user, shopify_store_data):
    """Test the connection testing endpoint"""
    # Mock successful connection
    mock_shopify_test.return_value = {
        "success": True,
        "message": "Connection successful",
        "store_info": {"name": "Test Store"}
    }
    
    response = client.post("/api/stores/test-connection", json=shopify_store_data, headers=authenticated_user)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Connection successful" in data["message"]

def test_test_connection_unsupported_platform(authenticated_user):
    """Test connection test with unsupported platform"""
    invalid_store = {
        "name": "Test Store",
        "platform": "unsupported",
        "store_url": "test-store.com",
        "api_key": "test_key",
        "api_secret": "test_secret"
    }
    
    response = client.post("/api/stores/test-connection", json=invalid_store, headers=authenticated_user)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is False
    assert "Unsupported platform" in data["message"]

@patch('routers.stores.test_shopify_connection')
def test_get_stores_with_data(mock_shopify_test, authenticated_user, shopify_store_data):
    """Test getting stores after creating one"""
    # Mock successful connection
    mock_shopify_test.return_value = {
        "success": True,
        "message": "Connection successful",
        "store_info": {"name": "Test Store"}
    }
    
    # Create a store
    client.post("/api/stores/", json=shopify_store_data, headers=authenticated_user)
    
    # Get stores
    response = client.get("/api/stores/", headers=authenticated_user)
    assert response.status_code == 200
    stores = response.json()
    assert len(stores) == 1
    assert stores[0]["name"] == shopify_store_data["name"]

def test_create_store_invalid_data(authenticated_user):
    """Test creating store with invalid data"""
    invalid_store = {
        "name": "",  # Empty name
        "platform": "shopify",
        "store_url": "invalid-url",
        "api_key": "",  # Empty API key
        "api_secret": ""
    }
    
    response = client.post("/api/stores/", json=invalid_store, headers=authenticated_user)
    assert response.status_code == 422  # Validation error
