from logging.config import fileConfig
import os
import sys
import importlib
import pkgutil
import logging

from sqlalchemy import engine_from_config
from sqlalchemy import pool
from sqlalchemy.ext.declarative import declarative_base

from alembic import context

# Set up logging
logger = logging.getLogger("alembic.env")

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
logger.info(f"Added project root to Python path: {project_root}")

# Create a Base instance for models to be discovered
Base = declarative_base()

# Dynamically import all models to ensure they're registered with Base.metadata
def import_all_models():
    """Automatically import all models from common locations."""
    # Common model locations to check
    model_locations = [
        "models",
        "app.models",
        "backend.models",
        "app",
        "backend"
    ]
    
    logger.info("Searching for models in the following locations: %s", model_locations)
    
    for location in model_locations:
        try:
            # Try importing as a module
            logger.info(f"Attempting to import {location}")
            module = importlib.import_module(location)
            logger.info(f"Successfully imported {location}")
            
            # If it's a package, import all submodules
            if hasattr(module, "__path__"):
                logger.info(f"{location} is a package, searching for submodules")
                for _, name, is_pkg in pkgutil.iter_modules(module.__path__):
                    if not is_pkg:  # Only import non-package modules
                        try:
                            submodule_name = f"{location}.{name}"
                            logger.info(f"Importing submodule {submodule_name}")
                            importlib.import_module(submodule_name)
                            logger.info(f"Successfully imported {submodule_name}")
                        except ImportError as e:
                            logger.info(f"Failed to import {location}.{name}: {e}")
        except ImportError as e:
            logger.info(f"Failed to import {location}: {e}")
            continue

# Import all models
logger.info("Starting model import process")
import_all_models()
logger.info("Completed model import process")

# Try to find the Base class with metadata in common locations
def find_metadata():
    """Find SQLAlchemy metadata from common locations."""
    # Check common locations for Base or metadata
    possible_locations = [
        ("models", "Base"),
        ("app.models", "Base"),
        ("backend.models", "Base"),
        ("app", "Base"),
        ("backend", "Base"),
        ("db", "Base"),
        ("database", "Base")
    ]
    
    logger.info("Searching for SQLAlchemy Base in: %s", possible_locations)
    
    for module_name, attr_name in possible_locations:
        try:
            logger.info(f"Checking for {attr_name} in {module_name}")
            module = importlib.import_module(module_name)
            if hasattr(module, attr_name):
                base_class = getattr(module, attr_name)
                if hasattr(base_class, "metadata"):
                    logger.info(f"Found metadata in {module_name}.{attr_name}")
                    return base_class.metadata
                else:
                    logger.info(f"{module_name}.{attr_name} exists but has no metadata attribute")
            else:
                logger.info(f"{module_name} exists but has no {attr_name} attribute")
        except ImportError as e:
            logger.info(f"Failed to import {module_name}: {e}")
            continue
    
    # If we couldn't find an existing Base with metadata, use our local Base
    # This is a fallback to ensure we at least have a metadata object
    logger.info("No external metadata found, using local Base.metadata")
    return Base.metadata

# Set target_metadata to the discovered metadata or None
target_metadata = find_metadata()
logger.info(f"Using target_metadata: {target_metadata}")
if target_metadata:
    # Log the tables found in the metadata
    table_names = list(target_metadata.tables.keys())
    logger.info(f"Tables found in metadata: {table_names}")
else:
    logger.warning("No tables found in metadata!")

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
