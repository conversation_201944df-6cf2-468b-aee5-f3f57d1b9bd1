import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  User,
  CreditCard,
  Package,
  Truck,
  DollarSign,
  ShoppingBag,
  Mail,
  Hash,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { fetchOrderById, type Order } from "../services/orderService";

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadOrder(parseInt(id));
    }
  }, [id]);

  const loadOrder = async (orderId: number) => {
    try {
      setLoading(true);
      const orderData = await fetchOrderById(orderId);
      setOrder(orderData);
    } catch (error) {
      console.error("Failed to load order:", error);
      setError("Failed to load order details");
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "fulfilled":
      case "paid":
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
      case "processing":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
      case "refunded":
        return "bg-red-100 text-red-800 border-red-200";
      case "partially_fulfilled":
      case "partially_paid":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "fulfilled":
      case "paid":
      case "completed":
        return <CheckCircle size={16} />;
      case "pending":
      case "processing":
        return <Clock size={16} />;
      case "cancelled":
      case "refunded":
        return <AlertCircle size={16} />;
      default:
        return <Package size={16} />;
    }
  };

  const formatCurrency = (
    amount: number | undefined,
    currency: string = "USD"
  ) => {
    if (amount === undefined || amount === null) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="text-center py-12">
          <AlertCircle className="mx-auto text-red-400 mb-4" size={64} />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            {error || "Order not found"}
          </h3>
          <button
            onClick={() => navigate("/orders")}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            ← Back to Orders
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate("/orders")}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft size={20} />
            Back to Orders
          </button>
          <div className="h-6 w-px bg-gray-300"></div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Order {order.order_number || `#${order.external_id}`}
            </h1>
            <p className="text-gray-600 mt-1">
              Placed on{" "}
              {new Date(order.order_date).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Order Items */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <ShoppingBag size={20} />
                Order Items ({order.line_items?.length || 0})
              </h2>
            </div>
            <div className="divide-y divide-gray-200">
              {order.line_items && order.line_items.length > 0 ? (
                order.line_items.map((item, index) => (
                  <div
                    key={item.id || index}
                    className="p-6 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 mb-1">
                          {item.title}
                        </h3>
                        {item.sku && (
                          <p className="text-sm text-gray-500 mb-1">
                            SKU: {item.sku}
                          </p>
                        )}
                        {item.vendor && (
                          <p className="text-sm text-gray-500">
                            Vendor: {item.vendor}
                          </p>
                        )}
                      </div>
                      <div className="text-right ml-4">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(
                            item.price * item.quantity,
                            order.currency
                          )}
                        </p>
                        <p className="text-sm text-gray-500">
                          {item.quantity} ×{" "}
                          {formatCurrency(item.price, order.currency)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-6 text-center text-gray-500">
                  No items found for this order
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Status */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Order Status
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Order Status:</span>
                <span
                  className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(
                    order.status
                  )}`}
                >
                  {getStatusIcon(order.status)}
                  {order.status}
                </span>
              </div>
              {order.financial_status && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Payment:</span>
                  <span
                    className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(
                      order.financial_status
                    )}`}
                  >
                    <CreditCard size={12} />
                    {order.financial_status}
                  </span>
                </div>
              )}
              {order.fulfillment_status && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Fulfillment:</span>
                  <span
                    className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(
                      order.fulfillment_status
                    )}`}
                  >
                    <Truck size={12} />
                    {order.fulfillment_status}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <User size={18} />
              Customer
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Mail size={16} className="text-gray-400" />
                <span className="text-gray-900">
                  {order.customer_email || "No email provided"}
                </span>
              </div>
              {order.customer_id && (
                <div className="flex items-center gap-2">
                  <Hash size={16} className="text-gray-400" />
                  <span className="text-gray-600 text-sm">
                    ID: {order.customer_id}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <DollarSign size={18} />
              Order Summary
            </h3>
            <div className="space-y-3">
              {order.subtotal_price !== undefined && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="text-gray-900">
                    {formatCurrency(order.subtotal_price, order.currency)}
                  </span>
                </div>
              )}
              {order.total_discounts !== undefined &&
                order.total_discounts > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Discounts:</span>
                    <span className="text-red-600">
                      -{formatCurrency(order.total_discounts, order.currency)}
                    </span>
                  </div>
                )}
              {order.total_tax !== undefined && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax:</span>
                  <span className="text-gray-900">
                    {formatCurrency(order.total_tax, order.currency)}
                  </span>
                </div>
              )}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold text-gray-900">
                    Total:
                  </span>
                  <span className="text-lg font-semibold text-gray-900">
                    {formatCurrency(order.total_price, order.currency)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Order Details */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Order Details
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Order ID:</span>
                <span className="text-gray-900 font-mono">
                  #{order.external_id}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Order Date:</span>
                <span className="text-gray-900">
                  {new Date(order.order_date).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Currency:</span>
                <span className="text-gray-900">{order.currency}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetail;
