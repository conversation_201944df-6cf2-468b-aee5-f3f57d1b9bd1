from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://user:password@localhost/ecommerce_db"
    
    # JWT
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # GCP
    gcp_project_id: Optional[str] = None
    gcp_region: str = "us-central1"
    
    # Shopify
    shopify_api_key: Optional[str] = None
    shopify_api_secret: Optional[str] = None
    
    # WooCommerce
    woocommerce_url: Optional[str] = None
    woocommerce_consumer_key: Optional[str] = None
    woocommerce_consumer_secret: Optional[str] = None
    
    # Environment
    environment: str = "development"
    
    class Config:
        env_file = ".env"

settings = Settings()
