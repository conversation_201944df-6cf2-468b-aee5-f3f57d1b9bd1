import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Package, Search, Grid, List } from "lucide-react";
import { api } from "../services/api";
import ProductCard from "../components/ProductCard";
import { useScroll } from "../contexts/ScrollContext";

interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  description_html?: string;
  handle?: string;
  price: number;
  compare_at_price?: number;
  inventory_quantity: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  weight_unit?: string;
  status: string;
  product_type?: string;
  vendor?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  store_id: number;
  order_count?: number; // <-- Add order count
}

interface Store {
  id: number;
  name: string;
  shop_name?: string;
  last_sync?: string; // Add last_sync to Store interface
}

const Products: React.FC = () => {
  const navigate = useNavigate();
  const { saveScrollPosition, restoreScrollPosition } = useScroll();
  const [products, setProducts] = useState<Product[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<number | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  useEffect(() => {
    loadProducts();
    loadStores();
    // Restore scroll position when returning to products page
    restoreScrollPosition("products-list");
  }, [restoreScrollPosition]);

  const loadProducts = async () => {
    try {
      const response = await api.get("/api/products/", {
        params: selectedStore ? { store_id: selectedStore } : {},
      });
      setProducts(response.data);
    } catch (error) {
      console.error("Failed to load products:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadStores = async () => {
    try {
      const response = await api.get("/api/stores/");
      setStores(response.data);
    } catch (error) {
      console.error("Failed to load stores:", error);
    }
  };

  const handleProductClick = async (product: Product) => {
    // Save current scroll position before navigating
    saveScrollPosition("products-list");
    navigate(`/products/${product.id}`);
  };

  const filteredProducts = products.filter((product) => {
    const matchesSearch =
      product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.vendor?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || product.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Products</h1>
          <p className="text-gray-600">
            Manage your product catalog with interactive sales forecasting
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode("grid")}
            className={`p-2 rounded-lg ${
              viewMode === "grid"
                ? "bg-blue-500 text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          >
            <Grid size={20} />
          </button>
          <button
            onClick={() => setViewMode("list")}
            className={`p-2 rounded-lg ${
              viewMode === "list"
                ? "bg-blue-500 text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          >
            <List size={20} />
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search */}
        <div className="relative">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Store Filter */}
        <select
          value={selectedStore || ""}
          onChange={(e) =>
            setSelectedStore(e.target.value ? parseInt(e.target.value) : null)
          }
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">All Stores</option>
          {stores.map((store) => (
            <option key={store.id} value={store.id}>
              {store.shop_name || store.name}
              {store.last_sync
                ? ` (Last sync: ${new Date(store.last_sync).toLocaleString()})`
                : ""}
            </option>
          ))}
        </select>

        {/* Status Filter */}
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="draft">Draft</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Products Display */}
      {filteredProducts.length > 0 ? (
        <div
          className={
            viewMode === "grid"
              ? "grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              : "space-y-4"
          }
        >
          {filteredProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onClick={handleProductClick}
              className={viewMode === "list" ? "flex-row" : ""}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Package className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            {searchTerm ? "No products found" : "No products yet"}
          </h3>
          <p className="text-gray-500">
            {searchTerm
              ? "Try adjusting your search terms or filters"
              : "Connect a store and sync your products to see them here"}
          </p>
        </div>
      )}
    </div>
  );
};

export default Products;
