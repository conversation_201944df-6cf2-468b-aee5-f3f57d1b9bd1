#!/usr/bin/env python3

import sys
import os

# Add the virtual environment packages to Python path
venv_path = os.path.join(os.path.dirname(__file__), 'venv', 'lib', 'python3.12', 'site-packages')
sys.path.insert(0, venv_path)

try:
    from main import app
    print("✅ FastAPI app imported successfully!")
    
    # Test basic functionality
    from fastapi.testclient import TestClient
    client = TestClient(app)
    
    # Test root endpoint
    response = client.get("/")
    print(f"✅ Root endpoint: {response.status_code} - {response.json()}")
    
    # Test health endpoint
    response = client.get("/health")
    print(f"✅ Health endpoint: {response.status_code} - {response.json()}")
    
    print("\n🎉 Backend is working correctly!")
    print("📝 To start the server manually, run:")
    print("   PYTHONPATH=venv/lib/python3.12/site-packages python3 -m uvicorn main:app --host 0.0.0.0 --port 8000")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("💡 Make sure you've installed the dependencies:")
    print("   python3 -m venv venv")
    print("   venv/bin/pip install -r requirements.txt")
