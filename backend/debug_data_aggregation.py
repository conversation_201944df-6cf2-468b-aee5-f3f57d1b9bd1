#!/usr/bin/env python3
"""
Debug data aggregation to understand the Prophet prediction issue
"""

import requests
import json
from collections import defaultdict

def debug_data_aggregation():
    """Debug how the sales data is being aggregated"""
    
    print("🔍 Debugging Data Aggregation...")
    print("=" * 60)
    
    try:
        # Get the debug forecast data
        response = requests.get("http://localhost:8000/api/products/debug/1/forecast?forecast_period=1M")
        
        if response.status_code != 200:
            print(f"❌ Failed to get forecast data: {response.status_code}")
            return
            
        data = response.json()
        
        if not data.get("success"):
            print(f"❌ Forecast failed: {data.get('message', 'Unknown error')}")
            return
            
        historical = data.get("historical_data", [])
        forecast_data = data.get("forecast_data", [])
        
        print(f"📊 Raw Historical Data Analysis:")
        print(f"  Total records: {len(historical)}")
        
        if historical:
            # Group by date to see the aggregation issue
            daily_totals = defaultdict(int)
            daily_counts = defaultdict(int)
            
            for record in historical:
                date = record["date"][:10]  # Get just YYYY-MM-DD
                daily_totals[date] += record["actual_sales"]
                daily_counts[date] += 1
            
            # Sort by date
            sorted_dates = sorted(daily_totals.keys())
            
            print(f"  Unique dates: {len(sorted_dates)}")
            print(f"  Date range: {sorted_dates[0]} to {sorted_dates[-1]}")
            
            # Show some examples of daily aggregation
            print(f"\n📅 Daily Aggregation Examples:")
            for i, date in enumerate(sorted_dates[:10]):  # First 10 days
                total = daily_totals[date]
                count = daily_counts[date]
                print(f"  {date}: {total} units ({count} records)")
            
            if len(sorted_dates) > 10:
                print(f"  ... (showing first 10 of {len(sorted_dates)} days)")
            
            # Calculate statistics
            all_daily_totals = list(daily_totals.values())
            avg_daily = sum(all_daily_totals) / len(all_daily_totals)
            max_daily = max(all_daily_totals)
            min_daily = min(all_daily_totals)
            
            print(f"\n📈 Daily Totals Statistics:")
            print(f"  Average daily sales: {avg_daily:.2f} units/day")
            print(f"  Range: {min_daily} to {max_daily} units/day")
            print(f"  Days with >10 units: {sum(1 for x in all_daily_totals if x > 10)}")
            print(f"  Days with >20 units: {sum(1 for x in all_daily_totals if x > 20)}")
            
            # Show the highest sales days
            high_days = [(date, daily_totals[date]) for date in sorted_dates if daily_totals[date] > 10]
            if high_days:
                print(f"\n🔥 High Sales Days:")
                for date, total in sorted(high_days, key=lambda x: x[1], reverse=True)[:5]:
                    count = daily_counts[date]
                    print(f"  {date}: {total} units ({count} records)")
        
        # Compare with predictions
        if forecast_data:
            predictions = [p["predicted_sales"] for p in forecast_data[:7]]
            pred_avg = sum(predictions) / len(predictions)
            
            print(f"\n🔮 Forecast Comparison:")
            print(f"  Predicted average (first week): {pred_avg:.2f} units/day")
            print(f"  Historical average: {avg_daily:.2f} units/day")
            print(f"  Ratio (pred/actual): {pred_avg/avg_daily:.2f}")
            
            print(f"\n💡 Analysis:")
            if pred_avg < avg_daily * 0.5:
                print(f"  ❌ PROBLEM: Predictions are much lower than historical data")
                print(f"     This suggests the models are not seeing the aggregated daily totals")
                print(f"     The preprocessing might not be working correctly")
            elif pred_avg > avg_daily * 2:
                print(f"  ❌ PROBLEM: Predictions are much higher than historical data")
                print(f"     This suggests overfitting or data issues")
            else:
                print(f"  ✅ GOOD: Predictions are reasonably close to historical data")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_aggregation()
