import React, { useState } from "react";
import { Package, Eye, Tag } from "lucide-react";

interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  description_html?: string;
  handle?: string;
  price: number;
  compare_at_price?: number;
  inventory_quantity: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  weight_unit?: string;
  status: string;
  product_type?: string;
  vendor?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  store_id: number;
  order_count?: number; // <-- Add order count
}

interface ProductCardProps {
  product: Product;
  onClick?: (product: Product) => void;
  className?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onClick,
  className = "",
}) => {
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleCardClick = () => {
    if (onClick) {
      onClick(product);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      case "archived":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getInventoryStatus = (quantity: number) => {
    if (quantity === 0) {
      return { color: "text-red-500", label: "Out of Stock", icon: "●" };
    } else if (quantity < 10) {
      return { color: "text-yellow-500", label: "Low Stock", icon: "●" };
    } else {
      return { color: "text-green-500", label: "In Stock", icon: "●" };
    }
  };

  const inventoryStatus = getInventoryStatus(product.inventory_quantity);
  const hasDiscount =
    product.compare_at_price && product.compare_at_price > product.price;
  const discountPercentage = hasDiscount
    ? Math.round(
        ((product.compare_at_price! - product.price) /
          product.compare_at_price!) *
          100
      )
    : 0;

  const primaryImage =
    product.images && product.images.length > 0 ? product.images[0] : null;

  return (
    <div
      className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:-translate-y-1 ${className}`}
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ minHeight: 420, display: "flex", flexDirection: "column" }}
    >
      {/* Product Image */}
      <div className="relative h-48 bg-gray-100 rounded-t-lg overflow-hidden">
        {primaryImage && !imageError ? (
          <img
            src={primaryImage}
            alt={product.title}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <Package className="text-gray-400" size={48} />
          </div>
        )}

        {/* Status Badge */}
        <div className="absolute top-2 left-2">
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
              product.status
            )}`}
          >
            {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
          </span>
        </div>

        {/* Discount Badge */}
        {hasDiscount && (
          <div className="absolute top-2 right-2">
            <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
              -{discountPercentage}%
            </span>
          </div>
        )}

        {/* Hover Actions */}
        {isHovered && (
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center space-x-2 transition-opacity duration-300">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCardClick();
              }}
              className="bg-white text-gray-800 p-2 rounded-full hover:bg-gray-100 transition-colors"
              title="View Details"
            >
              <Eye size={16} />
            </button>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4 flex flex-col justify-between h-full flex-1">
        {/* Title and Vendor */}
        <div className="mb-2">
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-2 mb-1">
            {product.title}
          </h3>
          {product.vendor && (
            <p className="text-sm text-gray-600">by {product.vendor}</p>
          )}
        </div>

        {/* SKU */}
        {product.sku && (
          <div className="flex items-center text-xs text-gray-500 mb-2">
            <Tag size={12} className="mr-1" />
            SKU: {product.sku}
          </div>
        )}

        {/* Price */}
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-xl font-bold text-gray-900">
            ${product.price.toFixed(2)}
          </span>
          {hasDiscount && (
            <span className="text-sm text-gray-500 line-through">
              ${product.compare_at_price!.toFixed(2)}
            </span>
          )}
        </div>

        {/* Inventory Status */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-1">
            <span className={inventoryStatus.color}>
              {inventoryStatus.icon}
            </span>
            <span className="text-sm text-gray-600">
              {inventoryStatus.label}
            </span>
          </div>
          <span className="text-sm font-medium text-gray-900">
            {product.inventory_quantity} units
          </span>
        </div>

        {/* Order Count */}
        {typeof product.order_count === "number" && (
          <div className="flex items-center mb-3 text-sm text-gray-700">
            <svg
              className="w-4 h-4 mr-1 text-blue-500"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13l-1.35 2.7A2 2 0 0 0 7.48 19h9.04a2 2 0 0 0 1.83-1.3L21 13M7 13V6h13"
              />
            </svg>
            <span>
              {product.order_count} order{product.order_count === 1 ? "" : "s"}
            </span>
          </div>
        )}

        {/* Product Type */}
        {product.product_type && (
          <div className="mb-3">
            <span className="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
              {product.product_type}
            </span>
          </div>
        )}

        {/* Tags */}
        {product.tags && product.tags.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {product.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-block bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs"
                >
                  {tag}
                </span>
              ))}
              {product.tags.length > 3 && (
                <span className="inline-block bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  +{product.tags.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Action Button */}
        <div className="mt-4">
          <button
            onClick={handleCardClick}
            className="w-full bg-blue-500 text-white py-2 px-3 rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
          >
            <Eye size={14} />
            <span>View Details & Forecast</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
