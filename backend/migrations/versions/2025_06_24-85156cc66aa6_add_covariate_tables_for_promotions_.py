"""Add covariate tables for promotions holidays and price history

Revision ID: 85156cc66aa6
Revises: 16d58ed9c2e0
Create Date: 2025-06-24 11:51:01.128616

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "85156cc66aa6"
down_revision: Union[str, Sequence[str], None] = "16d58ed9c2e0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "holidays",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("country", sa.String(), nullable=True),
        sa.Column("holiday_type", sa.String(), nullable=False),
        sa.Column("impact_level", sa.Integer(), nullable=True),
        sa.Column("is_recurring", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=True
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_holidays_id"), "holidays", ["id"], unique=False)
    op.create_table(
        "product_categories",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("parent_id", sa.Integer(), nullable=True),
        sa.Column("seasonality_pattern", sa.String(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["parent_id"],
            ["product_categories.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_index(op.f("ix_product_categories_id"), "product_categories", ["id"], unique=False)
    op.create_table(
        "price_history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("product_id", sa.Integer(), nullable=True),
        sa.Column("price", sa.Float(), nullable=False),
        sa.Column("compare_at_price", sa.Float(), nullable=True),
        sa.Column("effective_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["product_id"],
            ["products.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_price_history_id"), "price_history", ["id"], unique=False)
    op.create_table(
        "promotions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("discount_type", sa.String(), nullable=False),
        sa.Column("discount_value", sa.Float(), nullable=False),
        sa.Column("start_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("product_id", sa.Integer(), nullable=True),
        sa.Column("category", sa.String(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["product_id"],
            ["products.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_promotions_id"), "promotions", ["id"], unique=False)
    # Use batch mode for SQLite compatibility
    with op.batch_alter_table("sales_data", schema=None) as batch_op:
        batch_op.add_column(sa.Column("price", sa.Float(), nullable=True))
        batch_op.add_column(sa.Column("discount_amount", sa.Float(), nullable=True))
        batch_op.add_column(sa.Column("promotion_id", sa.Integer(), nullable=True))
        batch_op.create_foreign_key("fk_sales_data_promotion_id", "promotions", ["promotion_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Use batch mode for SQLite compatibility
    with op.batch_alter_table("sales_data", schema=None) as batch_op:
        batch_op.drop_constraint("fk_sales_data_promotion_id", type_="foreignkey")
        batch_op.drop_column("promotion_id")
        batch_op.drop_column("discount_amount")
        batch_op.drop_column("price")
    op.drop_index(op.f("ix_promotions_id"), table_name="promotions")
    op.drop_table("promotions")
    op.drop_index(op.f("ix_price_history_id"), table_name="price_history")
    op.drop_table("price_history")
    op.drop_index(op.f("ix_product_categories_id"), table_name="product_categories")
    op.drop_table("product_categories")
    op.drop_index(op.f("ix_holidays_id"), table_name="holidays")
    op.drop_table("holidays")
    # ### end Alembic commands ###
