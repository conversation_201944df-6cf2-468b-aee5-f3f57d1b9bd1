#!/usr/bin/env python3
"""
Simple script to check actual sales data vs predictions
"""

import requests
import json


def check_sales_reality():
    """Check if predictions match reality by examining the debug forecast data"""

    print("🔍 Checking Sales Reality vs Predictions...")
    print("=" * 60)

    try:
        # Get the debug forecast which should include historical data
        response = requests.get("http://localhost:8000/api/products/debug/1/forecast?forecast_period=1M")

        if response.status_code != 200:
            print(f"❌ Failed to get forecast data: {response.status_code}")
            return

        data = response.json()

        # Check response structure
        print(f"📋 Response keys: {list(data.keys())}")

        # Get predictions - handle both old and new response formats
        forecasts = data.get("forecasts", {})
        forecast_data = data.get("forecast_data", [])

        prophet_pred = None
        fb_prophet_pred = None
        ensemble_pred = None

        # Try to get individual model predictions
        if forecasts:
            if "Prophet" in forecasts and forecasts["Prophet"].get("forecast_data"):
                prophet_pred = forecasts["Prophet"]["forecast_data"][0]["predicted_sales"]

            if "FB_Prophet" in forecasts and forecasts["FB_Prophet"].get("forecast_data"):
                fb_prophet_pred = forecasts["FB_Prophet"]["forecast_data"][0]["predicted_sales"]

        # Try to get ensemble prediction
        if forecast_data:
            ensemble_pred = forecast_data[0]["predicted_sales"]

        print(f"📈 Current Predictions:")
        if prophet_pred is not None:
            print(f"  Prophet: {prophet_pred:.2f} units/day")
        if fb_prophet_pred is not None:
            print(f"  FB_Prophet: {fb_prophet_pred:.2f} units/day")
        if ensemble_pred is not None:
            print(f"  Ensemble: {ensemble_pred:.2f} units/day")

        if prophet_pred is not None and fb_prophet_pred is not None:
            diff = abs(fb_prophet_pred - prophet_pred)
            diff_pct = diff / prophet_pred * 100
            print(f"  Difference: {diff:.2f} units/day ({diff_pct:.1f}%)")
        elif prophet_pred is None and fb_prophet_pred is None:
            print("  ❌ No individual model predictions found")

        # Get historical data if available
        historical_data = data.get("historical_data", [])

        if historical_data:
            print(f"\n📊 Historical Data Analysis:")
            print(f"  Total historical records: {len(historical_data)}")

            # Extract actual sales values
            actual_sales = []
            for record in historical_data:
                if isinstance(record, dict):
                    sales = record.get("actual_sales", record.get("quantity_sold", 0))
                    if sales is not None:
                        actual_sales.append(sales)

            if actual_sales:
                # Calculate statistics
                total_sales = sum(actual_sales)
                avg_sales = total_sales / len(actual_sales)
                max_sales = max(actual_sales)
                min_sales = min(actual_sales)
                zero_days = sum(1 for s in actual_sales if s == 0)
                non_zero_sales = [s for s in actual_sales if s > 0]

                print(f"  Average daily sales: {avg_sales:.2f} units/day")
                print(f"  Min daily sales: {min_sales}")
                print(f"  Max daily sales: {max_sales}")
                print(f"  Zero sales days: {zero_days}/{len(actual_sales)} ({zero_days/len(actual_sales)*100:.1f}%)")

                if non_zero_sales:
                    avg_non_zero = sum(non_zero_sales) / len(non_zero_sales)
                    print(f"  Average on non-zero days: {avg_non_zero:.2f} units/day")

                # Recent sales (last 30 days)
                recent_sales = actual_sales[-30:] if len(actual_sales) >= 30 else actual_sales
                recent_avg = sum(recent_sales) / len(recent_sales)
                print(f"  Recent average ({len(recent_sales)} days): {recent_avg:.2f} units/day")

                # Show last 10 days
                print(f"\n📋 Last 10 Days of Sales:")
                for i, record in enumerate(historical_data[-10:]):
                    if isinstance(record, dict):
                        date = record.get("date", f"Day {i+1}")
                        sales = record.get("actual_sales", record.get("quantity_sold", 0))
                        print(f"  {date}: {sales} units")

                # Reality check
                print(f"\n🎯 Reality Check:")
                print(f"  Historical average: {avg_sales:.2f} units/day")
                print(f"  Recent average: {recent_avg:.2f} units/day")
                print(f"  Prophet prediction: {prophet_pred:.2f} units/day")
                print(f"  FB_Prophet prediction: {fb_prophet_pred:.2f} units/day")

                # Analysis
                print(f"\n💡 Analysis:")

                # Check if predictions are reasonable
                if recent_avg < 1 and prophet_pred > 4:
                    print(
                        f"  ❌ MAJOR ISSUE: Recent sales ({recent_avg:.2f}) are much lower than predictions ({prophet_pred:.2f})"
                    )
                    print(f"     Models may be overfitting to historical peaks or have preprocessing issues")
                elif recent_avg < 2 and prophet_pred > 6:
                    print(
                        f"  ⚠️  ISSUE: Recent sales ({recent_avg:.2f}) are lower than predictions ({prophet_pred:.2f})"
                    )
                elif abs(recent_avg - prophet_pred) <= 2:
                    print(f"  ✅ Predictions seem reasonable compared to recent sales")
                else:
                    print(f"  ⚠️  Predictions may be off by {abs(recent_avg - prophet_pred):.1f} units/day")

                # Check Prophet vs FB_Prophet difference
                diff_pct = abs(fb_prophet_pred - prophet_pred) / prophet_pred * 100
                if diff_pct > 50:
                    print(
                        f"  ❌ MAJOR ISSUE: Prophet models differ by {diff_pct:.1f}% - they should be nearly identical!"
                    )
                elif diff_pct > 20:
                    print(f"  ⚠️  ISSUE: Prophet models differ by {diff_pct:.1f}% - should be closer")
                else:
                    print(f"  ✅ Prophet models are reasonably close ({diff_pct:.1f}% difference)")

                # Sparse data analysis
                if zero_days / len(actual_sales) > 0.5:
                    print(
                        f"  💡 INSIGHT: {zero_days/len(actual_sales)*100:.1f}% zero-sales days indicates very sparse sales"
                    )
                    print(f"     This makes forecasting challenging and may explain prediction issues")

            else:
                print("  ❌ No actual sales data found in historical records")
        else:
            print("  ❌ No historical data available")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    check_sales_reality()
